"""
Configuration for Enhanced GitHub System

Environment-driven configuration with intelligent defaults for production deployment.
"""

import os
from typing import Dict, Any
from pathlib import Path

# Cache Configuration - Configurable via environment variables
CACHE_TIMEOUT_MINUTES = int(os.getenv('GITHUB_CACHE_TIMEOUT_MINUTES', '30'))
CACHE_STORAGE_PATH = os.getenv('GITHUB_CACHE_PATH', '/tmp/github_cache')
PERSISTENT_STORAGE_ENABLED = os.getenv('GITHUB_PERSISTENT_STORAGE', 'true').lower() == 'true'

# Google Cloud Storage Configuration
GCS_BUCKET_NAME = os.getenv('GITHUB_CACHE_GCS_BUCKET', 'enterprise-github-cache')
GCS_PROJECT_ID = os.getenv('GOOGLE_CLOUD_PROJECT')
GCS_ENABLED = os.getenv('GITHUB_USE_GCS', 'false').lower() == 'true'

# Parallel Processing Configuration
PARALLEL_DOWNLOAD_ENABLED = os.getenv('GITHUB_PARALLEL_DOWNLOAD', 'true').lower() == 'true'
DOWNLOAD_TIMEOUT_SECONDS = int(os.getenv('GITHUB_DOWNLOAD_TIMEOUT', '300'))
MAX_CONCURRENT_DOWNLOADS = int(os.getenv('GITHUB_MAX_CONCURRENT_DOWNLOADS', '3'))

# Fallback Configuration
ENABLE_MCP_FALLBACK = os.getenv('GITHUB_ENABLE_MCP_FALLBACK', 'true').lower() == 'true'
MCP_SERVER_URL = os.getenv('GITHUB_MCP_SERVER_URL', 'http://localhost:8080')
MCP_TIMEOUT_SECONDS = int(os.getenv('GITHUB_MCP_TIMEOUT', '30'))

# Analysis Configuration
MAX_COMMITS_ANALYZE = int(os.getenv('GITHUB_MAX_COMMITS_ANALYZE', '1000'))
ENABLE_DEVELOPER_METRICS = os.getenv('GITHUB_DEVELOPER_METRICS', 'true').lower() == 'true'

# Retry Configuration
MAX_DOWNLOAD_RETRIES = int(os.getenv('GITHUB_MAX_DOWNLOAD_RETRIES', '3'))
RETRY_BACKOFF_SECONDS = int(os.getenv('GITHUB_RETRY_BACKOFF', '5'))

# Security Configuration
GITHUB_TOKEN_REQUIRED = os.getenv('GITHUB_TOKEN_REQUIRED', 'false').lower() == 'true'
VALIDATE_REPO_ACCESS = os.getenv('GITHUB_VALIDATE_REPO_ACCESS', 'true').lower() == 'true'

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary."""
    return {
        'cache': {
            'timeout_minutes': CACHE_TIMEOUT_MINUTES,
            'storage_path': CACHE_STORAGE_PATH,
            'persistent_enabled': PERSISTENT_STORAGE_ENABLED,
            'gcs_bucket': GCS_BUCKET_NAME,
            'gcs_project': GCS_PROJECT_ID,
            'gcs_enabled': GCS_ENABLED
        },
        'parallel_processing': {
            'enabled': PARALLEL_DOWNLOAD_ENABLED,
            'download_timeout': DOWNLOAD_TIMEOUT_SECONDS,
            'max_concurrent': MAX_CONCURRENT_DOWNLOADS
        },
        'fallback': {
            'mcp_enabled': ENABLE_MCP_FALLBACK,
            'mcp_server_url': MCP_SERVER_URL,
            'mcp_timeout': MCP_TIMEOUT_SECONDS
        },
        'analysis': {
            'max_commits': MAX_COMMITS_ANALYZE,
            'developer_metrics': ENABLE_DEVELOPER_METRICS
        },
        'retry': {
            'max_retries': MAX_DOWNLOAD_RETRIES,
            'backoff_seconds': RETRY_BACKOFF_SECONDS
        },
        'security': {
            'token_required': GITHUB_TOKEN_REQUIRED,
            'validate_access': VALIDATE_REPO_ACCESS
        }
    }

def get_cache_timeout_seconds() -> int:
    """Get cache timeout in seconds."""
    return CACHE_TIMEOUT_MINUTES * 60

def is_cache_expired(last_modified_timestamp: float) -> bool:
    """Check if cache is expired based on timestamp."""
    import time
    current_time = time.time()
    age_seconds = current_time - last_modified_timestamp
    return age_seconds > get_cache_timeout_seconds()

def get_storage_path(repo_identifier: str) -> str:
    """Get storage path for a repository."""
    safe_repo_name = repo_identifier.replace('/', '_').replace('\\', '_')
    storage_path = Path(CACHE_STORAGE_PATH) / safe_repo_name
    storage_path.mkdir(parents=True, exist_ok=True)
    return str(storage_path)

def get_git_folder_path(repo_identifier: str) -> str:
    """Get .git folder path for a repository."""
    storage_path = get_storage_path(repo_identifier)
    return str(Path(storage_path) / ".git")
