"""
Git Downloader with Parallel Processing

Handles downloading .git folders with parallel processing, retry logic,
and integration with the cache manager.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Optional, Any, Callable
from concurrent.futures import ThreadPoolExecutor, Future
from pathlib import Path

from . import config
from . import cache_manager

# Import the existing git folder downloader
import sys
sys.path.append('.')
from git_folder_downloader_agent.agent import download_git_folder_from_github

logger = logging.getLogger(__name__)

class ParallelGitDownloader:
    """Manages parallel downloading of .git folders with intelligent retry and caching."""
    
    def __init__(self):
        self.config = config.get_config()
        self.cache_manager = cache_manager.cache_manager
        self.executor = ThreadPoolExecutor(max_workers=self.config['parallel_processing']['max_concurrent'])
        self.active_downloads = {}  # Track active downloads to avoid duplicates
    
    def download_async(self, 
                      repo_identifier: str, 
                      github_token: Optional[str] = None,
                      force_refresh: bool = False,
                      callback: Optional[Callable] = None) -> Future:
        """
        Start asynchronous download of .git folder.
        
        Args:
            repo_identifier: GitHub repository identifier (owner/repo)
            github_token: GitHub API token
            force_refresh: Force re-download even if cache exists
            callback: Optional callback function when download completes
            
        Returns:
            Future object for the download operation
        """
        logger.info(f"Starting async download for {repo_identifier}")
        
        # Check if download is already in progress
        if repo_identifier in self.active_downloads:
            logger.info(f"Download already in progress for {repo_identifier}")
            return self.active_downloads[repo_identifier]
        
        # Check if we need to download
        should_download, reason = self.cache_manager.should_download(repo_identifier, force_refresh)
        
        if not should_download:
            logger.info(f"Skipping download for {repo_identifier}: {reason}")
            # Return a completed future with cache info
            future = Future()
            cache_status = self.cache_manager.check_cache_status(repo_identifier)
            future.set_result({
                "success": True,
                "from_cache": True,
                "reason": reason,
                "cache_status": cache_status
            })
            return future
        
        # Start download
        future = self.executor.submit(
            self._download_with_retry,
            repo_identifier,
            github_token,
            callback
        )
        
        self.active_downloads[repo_identifier] = future
        
        # Clean up when done
        def cleanup(fut):
            if repo_identifier in self.active_downloads:
                del self.active_downloads[repo_identifier]
        
        future.add_done_callback(cleanup)
        
        return future
    
    def download_sync(self, 
                     repo_identifier: str, 
                     github_token: Optional[str] = None,
                     force_refresh: bool = False) -> Dict[str, Any]:
        """
        Synchronous download of .git folder.
        
        Args:
            repo_identifier: GitHub repository identifier (owner/repo)
            github_token: GitHub API token
            force_refresh: Force re-download even if cache exists
            
        Returns:
            Download result dictionary
        """
        future = self.download_async(repo_identifier, github_token, force_refresh)
        return future.result(timeout=self.config['parallel_processing']['download_timeout'])
    
    def _download_with_retry(self, 
                           repo_identifier: str, 
                           github_token: Optional[str],
                           callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Download with retry logic."""
        max_retries = self.config['retry']['max_retries']
        backoff_seconds = self.config['retry']['backoff_seconds']
        
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"Download attempt {attempt + 1}/{max_retries + 1} for {repo_identifier}")
                
                # Get target directory from cache manager
                target_dir = config.get_storage_path(repo_identifier)
                
                # Perform download
                start_time = time.time()
                result_json = download_git_folder_from_github(
                    repo_identifier, 
                    target_dir, 
                    github_token
                )
                download_time = time.time() - start_time
                
                result = json.loads(result_json)
                
                if result["success"]:
                    # Update cache metadata
                    self.cache_manager.update_cache_metadata(repo_identifier, result)
                    
                    # Add performance metrics
                    result["download_time_seconds"] = download_time
                    result["attempt_number"] = attempt + 1
                    result["from_cache"] = False
                    
                    logger.info(f"Successfully downloaded {repo_identifier} in {download_time:.2f}s")
                    
                    # Call callback if provided
                    if callback:
                        try:
                            callback(repo_identifier, result)
                        except Exception as e:
                            logger.error(f"Callback failed for {repo_identifier}: {e}")
                    
                    return result
                else:
                    last_error = result.get("error", "Unknown error")
                    logger.warning(f"Download failed for {repo_identifier}: {last_error}")
                    
            except Exception as e:
                last_error = str(e)
                logger.error(f"Download attempt {attempt + 1} failed for {repo_identifier}: {e}")
            
            # Wait before retry (except on last attempt)
            if attempt < max_retries:
                wait_time = backoff_seconds * (2 ** attempt)  # Exponential backoff
                logger.info(f"Waiting {wait_time}s before retry...")
                time.sleep(wait_time)
        
        # All attempts failed
        error_result = {
            "success": False,
            "error": f"Download failed after {max_retries + 1} attempts. Last error: {last_error}",
            "repo_identifier": repo_identifier,
            "attempts": max_retries + 1,
            "from_cache": False
        }
        
        logger.error(f"All download attempts failed for {repo_identifier}")
        return error_result
    
    def get_download_status(self, repo_identifier: str) -> Dict[str, Any]:
        """Get current download status for a repository."""
        status = {
            "repo_identifier": repo_identifier,
            "download_in_progress": repo_identifier in self.active_downloads,
            "cache_status": self.cache_manager.check_cache_status(repo_identifier)
        }
        
        if status["download_in_progress"]:
            future = self.active_downloads[repo_identifier]
            status["download_done"] = future.done()
            if future.done():
                try:
                    status["download_result"] = future.result()
                except Exception as e:
                    status["download_error"] = str(e)
        
        return status
    
    def cancel_download(self, repo_identifier: str) -> bool:
        """Cancel an active download."""
        if repo_identifier in self.active_downloads:
            future = self.active_downloads[repo_identifier]
            cancelled = future.cancel()
            if cancelled:
                del self.active_downloads[repo_identifier]
                logger.info(f"Cancelled download for {repo_identifier}")
            return cancelled
        return False
    
    def get_active_downloads(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all active downloads."""
        active = {}
        for repo_id, future in self.active_downloads.items():
            active[repo_id] = {
                "done": future.done(),
                "cancelled": future.cancelled(),
                "running": future.running()
            }
            if future.done():
                try:
                    active[repo_id]["result"] = future.result()
                except Exception as e:
                    active[repo_id]["error"] = str(e)
        return active
    
    def shutdown(self):
        """Shutdown the executor and cancel all downloads."""
        logger.info("Shutting down parallel downloader...")
        
        # Cancel all active downloads
        for repo_id in list(self.active_downloads.keys()):
            self.cancel_download(repo_id)
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        logger.info("Parallel downloader shutdown complete")

# Global downloader instance
downloader = ParallelGitDownloader()
