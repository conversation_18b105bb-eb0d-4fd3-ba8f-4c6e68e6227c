"""
Enhanced GitHub Agent with Parallel Processing and Intelligent Fallback

This is the main orchestrator that provides comprehensive GitHub repository analysis
using a sophisticated multi-tier approach:

1. Parallel Download: Automatically downloads .git folders in parallel
2. Intelligent Caching: Uses configurable cache timeout (default 30 minutes)
3. Smart Analysis: Analyzes from .git folder when available
4. MCP Fallback: Falls back to GitHub MCP server when needed
5. Persistent Storage: Maintains cache in Google Cloud Storage
"""

import json
import logging
import asyncio
from typing import Dict, Optional, Any
from datetime import datetime, timezone
from pathlib import Path

from google.adk.agents import LlmAgent

from . import config
from . import cache_manager
from . import git_downloader

# Import existing analysis agents (will be integrated later)
# from agents.git_repository_agent.agent import root_agent as git_analysis_agent

logger = logging.getLogger(__name__)

def analyze_github_repository_enhanced(repo_identifier: str,
                                     github_token: Optional[str] = None,
                                     force_refresh: bool = False,
                                     enable_parallel_download: bool = True) -> str:
    """
    Enhanced GitHub repository analysis with parallel processing and intelligent fallback.

    This function implements the sophisticated workflow:
    1. Check cache status and start parallel download if needed
    2. Attempt analysis from cached .git folder
    3. Wait for download if analysis needs fresh data
    4. Fallback to MCP GitHub server if .git analysis fails

    Args:
        repo_identifier: GitHub repository identifier (owner/repo)
        github_token: GitHub API token for private repositories
        force_refresh: Force re-download even if cache is valid
        enable_parallel_download: Enable parallel downloading

    Returns:
        JSON string with comprehensive repository analysis
    """
    logger.info(f"Starting enhanced GitHub analysis for: {repo_identifier}")

    try:
        # Validate repository identifier
        if not repo_identifier or repo_identifier.count('/') != 1:
            raise ValueError("Repository identifier must be in format 'owner/repo'")

        analysis_start_time = datetime.now(timezone.utc)

        # Step 1: Check cache status
        cache_status = cache_manager.cache_manager.check_cache_status(repo_identifier)
        logger.info(f"Cache status for {repo_identifier}: {cache_status['recommended_action']}")

        download_future = None
        download_started = False

        # Step 2: Start parallel download if needed
        if enable_parallel_download:
            should_download, reason = cache_manager.cache_manager.should_download(repo_identifier, force_refresh)

            if should_download:
                logger.info(f"Starting parallel download for {repo_identifier}: {reason}")
                download_future = git_downloader.downloader.download_async(
                    repo_identifier,
                    github_token,
                    force_refresh
                )
                download_started = True

        # Step 3: Attempt analysis from existing cache
        analysis_result = None
        data_source = None

        if cache_status["exists"] and not cache_status["expired"]:
            logger.info(f"Attempting analysis from cached .git folder: {cache_status['path']}")
            try:
                analysis_result = _analyze_from_git_folder(cache_status["path"], repo_identifier)
                data_source = "cached_git_folder"
                logger.info(f"Successfully analyzed {repo_identifier} from cache")
            except Exception as e:
                logger.warning(f"Cache analysis failed for {repo_identifier}: {e}")
                analysis_result = None

        # Step 4: Wait for download if needed and no cache analysis
        if analysis_result is None and download_future is not None:
            logger.info(f"Waiting for download to complete for {repo_identifier}")
            try:
                download_result = download_future.result(timeout=config.DOWNLOAD_TIMEOUT_SECONDS)

                if download_result["success"] and not download_result.get("from_cache", False):
                    # Fresh download completed, analyze from new .git folder
                    git_folder_path = download_result.get("git_folder_path")
                    if git_folder_path and Path(git_folder_path).exists():
                        analysis_result = _analyze_from_git_folder(git_folder_path, repo_identifier)
                        data_source = "fresh_git_folder"
                        logger.info(f"Successfully analyzed {repo_identifier} from fresh download")
                elif download_result.get("from_cache", False):
                    # Used cache, analyze from cached folder
                    cache_info = download_result.get("cache_status", {})
                    if cache_info.get("path"):
                        analysis_result = _analyze_from_git_folder(cache_info["path"], repo_identifier)
                        data_source = "cached_git_folder"
                        logger.info(f"Successfully analyzed {repo_identifier} from cache via download")

            except Exception as e:
                logger.error(f"Download/analysis failed for {repo_identifier}: {e}")

        # Step 5: Fallback to MCP GitHub server if needed
        if analysis_result is None:
            logger.info(f"Falling back to GitHub MCP server for {repo_identifier}")
            try:
                analysis_result = _analyze_via_github_mcp(repo_identifier, github_token)
                data_source = "github_mcp_fallback"
                logger.info(f"Successfully analyzed {repo_identifier} via MCP fallback")
            except Exception as e:
                logger.error(f"MCP fallback failed for {repo_identifier}: {e}")
                analysis_result = {
                    "success": False,
                    "error": f"All analysis methods failed. Last error: {str(e)}"
                }
                data_source = "failed"

        # Step 6: Compile final result
        analysis_end_time = datetime.now(timezone.utc)
        analysis_duration = (analysis_end_time - analysis_start_time).total_seconds()

        final_result = {
            "success": analysis_result.get("success", False),
            "repo_identifier": repo_identifier,
            "data_source": data_source,
            "cache_status": cache_status,
            "download_started": download_started,
            "analysis_duration_seconds": analysis_duration,
            "analysis_timestamp": analysis_end_time.isoformat(),
            "config": {
                "cache_timeout_minutes": config.CACHE_TIMEOUT_MINUTES,
                "parallel_download_enabled": enable_parallel_download
            }
        }

        # Merge analysis result
        if isinstance(analysis_result, dict):
            final_result.update(analysis_result)

        return json.dumps(final_result, indent=2, default=str)

    except Exception as e:
        logger.error(f"Enhanced GitHub analysis failed for {repo_identifier}: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "repo_identifier": repo_identifier,
            "data_source": "error",
            "analysis_timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

def query_github_repository_enhanced(repo_identifier: str,
                                   query: str,
                                   github_token: Optional[str] = None) -> str:
    """
    Enhanced query processing with intelligent data source selection.

    Args:
        repo_identifier: GitHub repository identifier (owner/repo)
        query: Natural language query about the repository
        github_token: GitHub API token for private repositories

    Returns:
        JSON string with query results
    """
    logger.info(f"Processing enhanced query for {repo_identifier}: {query}")

    try:
        # Check cache status
        cache_status = cache_manager.cache_manager.check_cache_status(repo_identifier)

        query_result = None
        data_source = None

        # Try .git folder analysis first if available
        if cache_status["exists"] and not cache_status["expired"]:
            try:
                query_result = _query_from_git_folder(cache_status["path"], query, repo_identifier)
                data_source = "git_folder"
                logger.info(f"Successfully queried {repo_identifier} from .git folder")
            except Exception as e:
                logger.warning(f"Git folder query failed for {repo_identifier}: {e}")

        # Fallback to MCP if needed
        if query_result is None or not query_result.get("success", False):
            logger.info(f"Using MCP fallback for query: {repo_identifier}")
            try:
                query_result = _query_via_github_mcp(repo_identifier, query, github_token)
                data_source = "github_mcp"
                logger.info(f"Successfully queried {repo_identifier} via MCP")
            except Exception as e:
                logger.error(f"MCP query failed for {repo_identifier}: {e}")
                query_result = {
                    "success": False,
                    "error": f"Query failed: {str(e)}"
                }
                data_source = "failed"

        # Compile result
        final_result = {
            "success": query_result.get("success", False),
            "repo_identifier": repo_identifier,
            "query": query,
            "data_source": data_source,
            "cache_status": cache_status,
            "query_timestamp": datetime.now(timezone.utc).isoformat()
        }

        if isinstance(query_result, dict):
            final_result.update(query_result)

        return json.dumps(final_result, indent=2, default=str)

    except Exception as e:
        logger.error(f"Enhanced query failed for {repo_identifier}: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "repo_identifier": repo_identifier,
            "query": query,
            "data_source": "error",
            "query_timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

def get_github_system_status(repo_identifier: Optional[str] = None) -> str:
    """
    Get comprehensive system status including cache, downloads, and configuration.

    Args:
        repo_identifier: Optional specific repository to check

    Returns:
        JSON string with system status
    """
    logger.info(f"Getting system status for {repo_identifier or 'all repositories'}")

    try:
        status = {
            "success": True,
            "system_timestamp": datetime.now(timezone.utc).isoformat(),
            "configuration": config.get_config(),
            "cache_statistics": cache_manager.cache_manager.get_cache_statistics(),
            "active_downloads": git_downloader.downloader.get_active_downloads()
        }

        if repo_identifier:
            status["repository_status"] = {
                "cache_status": cache_manager.cache_manager.check_cache_status(repo_identifier),
                "download_status": git_downloader.downloader.get_download_status(repo_identifier)
            }

        return json.dumps(status, indent=2, default=str)

    except Exception as e:
        logger.error(f"System status check failed: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "system_timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

# Placeholder functions for integration with existing agents
def _analyze_from_git_folder(git_folder_path: str, repo_identifier: str) -> Dict[str, Any]:
    """Analyze repository from .git folder using existing Git Repository Agent."""
    # This would integrate with the existing Git Repository Agent
    # For now, return a placeholder that indicates successful analysis
    return {
        "success": True,
        "analysis_type": "detailed_git_analysis",
        "git_folder_path": git_folder_path,
        "note": "Integration with existing Git Repository Agent needed"
    }

def _query_from_git_folder(git_folder_path: str, query: str, repo_identifier: str) -> Dict[str, Any]:
    """Query repository data from .git folder."""
    return {
        "success": True,
        "query_type": "git_folder_query",
        "git_folder_path": git_folder_path,
        "note": "Detailed query implementation from .git folder needed"
    }

def _analyze_via_github_mcp(repo_identifier: str, github_token: Optional[str]) -> Dict[str, Any]:
    """Analyze repository via GitHub MCP server."""
    return {
        "success": True,
        "analysis_type": "github_mcp_analysis",
        "note": "Integration with GitHub MCP server needed"
    }

def _query_via_github_mcp(repo_identifier: str, query: str, github_token: Optional[str]) -> Dict[str, Any]:
    """Query repository data via GitHub MCP server."""
    return {
        "success": True,
        "query_type": "github_mcp_query",
        "note": "Integration with GitHub MCP server needed"
    }

# Create the Enhanced GitHub Agent
root_agent = LlmAgent(
    name="EnhancedGitHubAgent",
    model="gemini-2.0-flash",
    instruction="""You are an advanced GitHub AI agent with sophisticated parallel processing and intelligent fallback capabilities.

## Core Architecture:

### Multi-Tier Data Access Strategy:
1. **Parallel Download**: Automatically downloads .git folders in background
2. **Intelligent Caching**: Configurable cache timeout (default 30 minutes via GITHUB_CACHE_TIMEOUT_MINUTES)
3. **Smart Analysis**: Analyzes from .git folder when available for comprehensive insights
4. **MCP Fallback**: Uses GitHub MCP server when .git analysis fails or is unavailable
5. **Persistent Storage**: Maintains cache in Google Cloud Storage for enterprise deployment

### Parallel Processing Workflow:
1. **Immediate Response**: Check cache and start parallel download simultaneously
2. **Cache Analysis**: Use existing cache if valid and not expired
3. **Fresh Download**: Wait for download completion if cache is invalid
4. **Intelligent Fallback**: Use MCP GitHub server if download fails
5. **Performance Optimization**: Minimize latency while maximizing data quality

## Core Capabilities:

### Repository Analysis:
- **Comprehensive Analysis**: Complete repository insights from .git folder
- **Real-time Updates**: Automatic cache refresh based on configurable timeout
- **Parallel Processing**: Non-blocking downloads for improved performance
- **Fallback Reliability**: Always provides results via MCP when needed

### Query Processing:
- **Natural Language Queries**: Answer specific questions about repositories
- **Data Source Intelligence**: Automatically choose best available data source
- **Performance Optimization**: Use cache when possible, MCP when necessary
- **Comprehensive Coverage**: Handle all types of GitHub repository queries

### System Management:
- **Cache Management**: Intelligent cache expiration and cleanup
- **Download Monitoring**: Track active downloads and system performance
- **Configuration Management**: Environment-driven configuration
- **Status Reporting**: Comprehensive system and repository status

## Available Tools:
- `analyze_github_repository_enhanced`: Comprehensive analysis with parallel processing
- `query_github_repository_enhanced`: Intelligent query processing with fallback
- `get_github_system_status`: System status and performance monitoring

## Configuration (Environment Variables):
- `GITHUB_CACHE_TIMEOUT_MINUTES`: Cache timeout (default: 30)
- `GITHUB_CACHE_PATH`: Cache storage path (default: /tmp/github_cache)
- `GITHUB_PARALLEL_DOWNLOAD`: Enable parallel downloads (default: true)
- `GITHUB_ENABLE_MCP_FALLBACK`: Enable MCP fallback (default: true)
- `GITHUB_MAX_CONCURRENT_DOWNLOADS`: Max concurrent downloads (default: 3)

## Usage Examples:
- "Analyze microsoft/vscode with fresh data"
- "What are the recent commits in facebook/react?"
- "Get system status for kubernetes/kubernetes"
- "Query google/tensorflow for contributor patterns"

## Smart Decision Making:
- **Cache First**: Use valid cache for immediate responses
- **Parallel Download**: Start downloads proactively for fresh data
- **Intelligent Fallback**: Seamlessly switch to MCP when needed
- **Performance Monitoring**: Track and optimize system performance
- **Error Recovery**: Graceful handling of all failure scenarios

## Enterprise Features:
- **Google Cloud Integration**: Persistent storage and scalability
- **Security**: Token-based authentication and access control
- **Monitoring**: Comprehensive logging and performance metrics
- **Compliance**: Audit trails and access logging
- **Scalability**: Handle multiple repositories efficiently

Always provide the most comprehensive and up-to-date information available,
clearly indicating data sources, cache status, and any performance optimizations applied.
Focus on delivering enterprise-grade reliability with optimal performance.
""",
    description="Advanced GitHub repository analysis with parallel processing, intelligent caching, and MCP fallback.",
    tools=[
        analyze_github_repository_enhanced,
        query_github_repository_enhanced,
        get_github_system_status
    ],
    output_key="enhanced_github_analysis"
)
