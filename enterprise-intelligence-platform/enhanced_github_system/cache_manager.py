"""
Cache Manager for Enhanced GitHub System

Manages intelligent caching of .git folders with configurable timeouts,
persistent storage, and Google Cloud Storage integration.
"""

import os
import json
import time
import logging
from pathlib import Path
from typing import Dict, Optional, Any, Tuple
from datetime import datetime, timezone

from . import config

logger = logging.getLogger(__name__)

class GitCacheManager:
    """Manages .git folder caching with intelligent expiration and persistence."""
    
    def __init__(self):
        self.config = config.get_config()
        self.cache_path = Path(self.config['cache']['storage_path'])
        self.cache_path.mkdir(parents=True, exist_ok=True)
    
    def check_cache_status(self, repo_identifier: str) -> Dict[str, Any]:
        """
        Check the cache status for a repository.
        
        Returns:
            Dict with cache status information
        """
        logger.debug(f"Checking cache status for {repo_identifier}")
        
        git_folder_path = config.get_git_folder_path(repo_identifier)
        metadata_path = Path(config.get_storage_path(repo_identifier)) / "cache_metadata.json"
        
        if not Path(git_folder_path).exists():
            return {
                "exists": False,
                "expired": True,
                "path": None,
                "last_modified": None,
                "age_minutes": None,
                "recommended_action": "download"
            }
        
        # Get file modification time
        stat_info = Path(git_folder_path).stat()
        last_modified = stat_info.st_mtime
        current_time = time.time()
        age_seconds = current_time - last_modified
        age_minutes = age_seconds / 60
        
        # Check if expired
        expired = config.is_cache_expired(last_modified)
        
        # Load metadata if available
        metadata = self._load_metadata(metadata_path)
        
        return {
            "exists": True,
            "expired": expired,
            "path": git_folder_path,
            "last_modified": datetime.fromtimestamp(last_modified, tz=timezone.utc).isoformat(),
            "age_minutes": age_minutes,
            "timeout_minutes": config.CACHE_TIMEOUT_MINUTES,
            "metadata": metadata,
            "recommended_action": "refresh" if expired else "use_cache"
        }
    
    def should_download(self, repo_identifier: str, force_refresh: bool = False) -> Tuple[bool, str]:
        """
        Determine if we should download .git folder.
        
        Returns:
            Tuple of (should_download, reason)
        """
        if force_refresh:
            return True, "force_refresh_requested"
        
        cache_status = self.check_cache_status(repo_identifier)
        
        if not cache_status["exists"]:
            return True, "cache_not_exists"
        
        if cache_status["expired"]:
            return True, "cache_expired"
        
        return False, "cache_valid"
    
    def update_cache_metadata(self, repo_identifier: str, download_result: Dict[str, Any]):
        """Update cache metadata after successful download."""
        try:
            storage_path = config.get_storage_path(repo_identifier)
            metadata_path = Path(storage_path) / "cache_metadata.json"
            
            metadata = {
                "repo_identifier": repo_identifier,
                "download_timestamp": download_result.get("timestamp"),
                "download_stats": download_result.get("download_stats"),
                "git_folder_path": download_result.get("git_folder_path"),
                "method": download_result.get("method"),
                "cache_timeout_minutes": config.CACHE_TIMEOUT_MINUTES,
                "last_updated": datetime.now(timezone.utc).isoformat()
            }
            
            metadata_path.write_text(json.dumps(metadata, indent=2))
            logger.info(f"Updated cache metadata for {repo_identifier}")
            
        except Exception as e:
            logger.error(f"Failed to update cache metadata for {repo_identifier}: {e}")
    
    def cleanup_expired_cache(self) -> Dict[str, Any]:
        """Clean up expired cache entries."""
        logger.info("Starting cache cleanup...")
        
        cleanup_stats = {
            "repositories_checked": 0,
            "expired_found": 0,
            "cleaned_up": 0,
            "errors": 0
        }
        
        try:
            for repo_dir in self.cache_path.iterdir():
                if repo_dir.is_dir():
                    cleanup_stats["repositories_checked"] += 1
                    repo_identifier = repo_dir.name.replace('_', '/')
                    
                    cache_status = self.check_cache_status(repo_identifier)
                    
                    if cache_status["expired"]:
                        cleanup_stats["expired_found"] += 1
                        
                        try:
                            # Remove the entire repository cache directory
                            import shutil
                            shutil.rmtree(repo_dir)
                            cleanup_stats["cleaned_up"] += 1
                            logger.info(f"Cleaned up expired cache for {repo_identifier}")
                        except Exception as e:
                            cleanup_stats["errors"] += 1
                            logger.error(f"Failed to cleanup cache for {repo_identifier}: {e}")
        
        except Exception as e:
            logger.error(f"Cache cleanup failed: {e}")
            cleanup_stats["errors"] += 1
        
        logger.info(f"Cache cleanup completed: {cleanup_stats}")
        return cleanup_stats
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get overall cache statistics."""
        stats = {
            "total_repositories": 0,
            "valid_cache": 0,
            "expired_cache": 0,
            "total_size_bytes": 0,
            "cache_path": str(self.cache_path),
            "cache_timeout_minutes": config.CACHE_TIMEOUT_MINUTES
        }
        
        try:
            for repo_dir in self.cache_path.iterdir():
                if repo_dir.is_dir():
                    stats["total_repositories"] += 1
                    repo_identifier = repo_dir.name.replace('_', '/')
                    
                    cache_status = self.check_cache_status(repo_identifier)
                    
                    if cache_status["exists"]:
                        if cache_status["expired"]:
                            stats["expired_cache"] += 1
                        else:
                            stats["valid_cache"] += 1
                        
                        # Calculate directory size
                        try:
                            for file_path in repo_dir.rglob('*'):
                                if file_path.is_file():
                                    stats["total_size_bytes"] += file_path.stat().st_size
                        except Exception as e:
                            logger.debug(f"Error calculating size for {repo_dir}: {e}")
        
        except Exception as e:
            logger.error(f"Failed to get cache statistics: {e}")
        
        return stats
    
    def _load_metadata(self, metadata_path: Path) -> Optional[Dict[str, Any]]:
        """Load metadata from file if it exists."""
        try:
            if metadata_path.exists():
                return json.loads(metadata_path.read_text())
        except Exception as e:
            logger.debug(f"Failed to load metadata from {metadata_path}: {e}")
        return None

# Global cache manager instance
cache_manager = GitCacheManager()
