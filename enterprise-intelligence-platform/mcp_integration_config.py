"""
Model Context Protocol (MCP) Integration Configuration

This module configures MCP integration for the ADK-Powered Enterprise Intelligence Platform,
leveraging Google's latest MCP capabilities including the MCP Toolbox for Databases and
secure enterprise-grade MCP server connections.
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

class MCPServerType(Enum):
    """Types of MCP servers supported."""
    DATABASE = "database"
    GITHUB = "github"
    FILESYSTEM = "filesystem"
    CUSTOM = "custom"

@dataclass
class MCPServerConfig:
    """Configuration for an MCP server."""
    name: str
    server_type: MCPServerType
    endpoint: str
    authentication: Dict[str, Any]
    security_level: str
    allowed_operations: List[str]
    rate_limits: Dict[str, int]
    timeout_seconds: int
    retry_config: Dict[str, Any]

# MCP Server Configurations
MCP_SERVERS = {
    "enterprise_database": MCPServerConfig(
        name="Enterprise Database MCP Server",
        server_type=MCPServerType.DATABASE,
        endpoint="https://mcp-database.internal.company.com",
        authentication={
            "type": "oauth2",
            "client_id": os.getenv("MCP_DB_CLIENT_ID"),
            "client_secret": os.getenv("MCP_DB_CLIENT_SECRET"),
            "token_url": "https://auth.company.com/oauth/token",
            "scopes": ["database.read", "database.query"]
        },
        security_level="high",
        allowed_operations=[
            "list_databases",
            "list_tables", 
            "describe_table",
            "execute_query",
            "get_schema"
        ],
        rate_limits={
            "requests_per_minute": 100,
            "queries_per_hour": 500,
            "max_concurrent": 10
        },
        timeout_seconds=30,
        retry_config={
            "max_retries": 3,
            "backoff_factor": 2,
            "retry_on_status": [500, 502, 503, 504]
        }
    ),
    
    "github_enterprise": MCPServerConfig(
        name="GitHub Enterprise MCP Server",
        server_type=MCPServerType.GITHUB,
        endpoint="https://mcp-github.internal.company.com",
        authentication={
            "type": "bearer_token",
            "token": os.getenv("GITHUB_ENTERPRISE_TOKEN"),
            "token_header": "Authorization"
        },
        security_level="medium",
        allowed_operations=[
            "list_repositories",
            "get_repository",
            "list_commits",
            "get_commit",
            "list_pull_requests",
            "get_pull_request",
            "search_code",
            "get_repository_stats"
        ],
        rate_limits={
            "requests_per_minute": 200,
            "api_calls_per_hour": 5000,
            "max_concurrent": 20
        },
        timeout_seconds=15,
        retry_config={
            "max_retries": 2,
            "backoff_factor": 1.5,
            "retry_on_status": [429, 500, 502, 503]
        }
    ),
    
    "cloud_storage": MCPServerConfig(
        name="Cloud Storage MCP Server",
        server_type=MCPServerType.FILESYSTEM,
        endpoint="https://mcp-storage.internal.company.com",
        authentication={
            "type": "service_account",
            "credentials_path": os.getenv("GOOGLE_APPLICATION_CREDENTIALS"),
            "project_id": os.getenv("GOOGLE_CLOUD_PROJECT")
        },
        security_level="high",
        allowed_operations=[
            "list_buckets",
            "list_objects",
            "get_object",
            "get_object_metadata",
            "search_objects"
        ],
        rate_limits={
            "requests_per_minute": 300,
            "data_transfer_mb_per_hour": 1000,
            "max_concurrent": 15
        },
        timeout_seconds=60,
        retry_config={
            "max_retries": 3,
            "backoff_factor": 2,
            "retry_on_status": [429, 500, 502, 503, 504]
        }
    )
}

# MCP Security Policies
MCP_SECURITY_POLICIES = {
    "authentication": {
        "require_authentication": True,
        "token_validation": True,
        "certificate_validation": True,
        "mutual_tls": True  # For high-security environments
    },
    
    "authorization": {
        "rbac_enabled": True,
        "operation_level_auth": True,
        "resource_level_auth": True,
        "audit_all_operations": True
    },
    
    "data_protection": {
        "encrypt_in_transit": True,
        "encrypt_at_rest": True,
        "data_masking": True,
        "pii_detection": True,
        "data_classification": True
    },
    
    "network_security": {
        "vpc_only": True,
        "firewall_rules": True,
        "ip_whitelisting": True,
        "ddos_protection": True
    }
}

# MCP Integration with ADK Agents
AGENT_MCP_MAPPINGS = {
    "git_repository_agent": {
        "primary_mcp_server": "github_enterprise",
        "fallback_servers": [],
        "operations": [
            "list_repositories",
            "get_repository",
            "list_commits",
            "get_repository_stats"
        ],
        "security_context": {
            "user_context_required": True,
            "permission_inheritance": True,
            "audit_level": "detailed"
        }
    },
    
    "database_query_agent": {
        "primary_mcp_server": "enterprise_database",
        "fallback_servers": [],
        "operations": [
            "list_databases",
            "list_tables",
            "describe_table",
            "execute_query"
        ],
        "security_context": {
            "user_context_required": True,
            "permission_inheritance": True,
            "audit_level": "comprehensive",
            "query_validation": True,
            "result_filtering": True
        }
    },
    
    "document_analysis_agent": {
        "primary_mcp_server": "cloud_storage",
        "fallback_servers": [],
        "operations": [
            "list_objects",
            "get_object",
            "get_object_metadata",
            "search_objects"
        ],
        "security_context": {
            "user_context_required": True,
            "permission_inheritance": True,
            "audit_level": "standard",
            "content_scanning": True
        }
    }
}

# Vertex AI Integration Configuration
VERTEX_AI_MCP_CONFIG = {
    "agent_engine": {
        "enabled": True,
        "project_id": os.getenv("GOOGLE_CLOUD_PROJECT"),
        "location": os.getenv("GOOGLE_CLOUD_LOCATION", "us-central1"),
        "agent_runtime": "vertex-ai-agent-engine"
    },
    
    "mcp_toolbox_database": {
        "enabled": True,
        "deployment_target": "cloud-run",
        "service_name": "mcp-database-toolbox",
        "min_instances": 1,
        "max_instances": 10,
        "cpu_limit": "2",
        "memory_limit": "4Gi",
        "environment_variables": {
            "DATABASE_URL": os.getenv("DATABASE_URL"),
            "GOOGLE_CLOUD_PROJECT": os.getenv("GOOGLE_CLOUD_PROJECT"),
            "LOG_LEVEL": "INFO"
        }
    },
    
    "observability": {
        "opentelemetry_enabled": True,
        "tracing_enabled": True,
        "metrics_enabled": True,
        "logging_level": "INFO",
        "export_to_cloud_monitoring": True,
        "export_to_cloud_trace": True
    }
}

# MCP Client Configuration for ADK Agents
MCP_CLIENT_CONFIG = {
    "connection_pool": {
        "max_connections": 50,
        "connection_timeout": 30,
        "idle_timeout": 300,
        "max_retries": 3
    },
    
    "circuit_breaker": {
        "enabled": True,
        "failure_threshold": 5,
        "recovery_timeout": 60,
        "half_open_max_calls": 3
    },
    
    "caching": {
        "enabled": True,
        "ttl_seconds": 300,
        "max_cache_size": 1000,
        "cache_key_strategy": "operation_params"
    },
    
    "monitoring": {
        "metrics_enabled": True,
        "latency_tracking": True,
        "error_tracking": True,
        "success_rate_tracking": True
    }
}

def get_mcp_server_config(server_name: str) -> Optional[MCPServerConfig]:
    """Get MCP server configuration by name."""
    return MCP_SERVERS.get(server_name)

def get_agent_mcp_config(agent_name: str) -> Optional[Dict[str, Any]]:
    """Get MCP configuration for a specific agent."""
    return AGENT_MCP_MAPPINGS.get(agent_name)

def validate_mcp_operation(server_name: str, operation: str, user_context: Dict[str, Any]) -> tuple[bool, str]:
    """
    Validate if an MCP operation is allowed for a user.
    
    Args:
        server_name: Name of the MCP server
        operation: Operation to validate
        user_context: User context including permissions
        
    Returns:
        Tuple of (is_allowed, error_message)
    """
    server_config = get_mcp_server_config(server_name)
    if not server_config:
        return False, f"Unknown MCP server: {server_name}"
    
    if operation not in server_config.allowed_operations:
        return False, f"Operation {operation} not allowed on server {server_name}"
    
    # Additional user-specific validation would go here
    # This would integrate with the enterprise RBAC system
    
    return True, ""

def get_mcp_security_context(agent_name: str, user_context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate MCP security context for an agent operation.
    
    Args:
        agent_name: Name of the agent
        user_context: User context including permissions
        
    Returns:
        Security context for MCP operations
    """
    agent_config = get_agent_mcp_config(agent_name)
    if not agent_config:
        return {}
    
    security_context = agent_config.get("security_context", {})
    
    return {
        "user_id": user_context.get("user_id"),
        "permissions": user_context.get("permissions", []),
        "audit_level": security_context.get("audit_level", "standard"),
        "require_user_context": security_context.get("user_context_required", False),
        "permission_inheritance": security_context.get("permission_inheritance", False)
    }

# Export main configuration
ENTERPRISE_MCP_CONFIG = {
    "servers": MCP_SERVERS,
    "security_policies": MCP_SECURITY_POLICIES,
    "agent_mappings": AGENT_MCP_MAPPINGS,
    "vertex_ai": VERTEX_AI_MCP_CONFIG,
    "client": MCP_CLIENT_CONFIG
}
