#!/usr/bin/env python3
"""
Test script for the Git Repository Agent

This script demonstrates the complete workflow of the Git Repository Agent:
1. Repository Acquisition: Clone a repository from GitHub
2. Repository Analysis: Analyze the .git folder for insights
3. Repository Cleanup: Clean up temporary files

Usage:
    python test_git_repository_agent.py
"""

import json
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append('.')

def test_git_repository_agent():
    """Test the complete Git Repository Agent workflow."""
    
    print("🚀 Testing Git Repository Agent Workflow")
    print("=" * 50)
    
    try:
        # Import the agents
        from agents.git_repository_agent.subagents.acquisition.agent import repository_acquisition_agent
        from agents.git_repository_agent.subagents.analysis.agent import repository_analysis_agent
        from agents.git_repository_agent.subagents.cleanup.agent import repository_cleanup_agent
        
        print("✓ Successfully imported all agents")
        
        # Step 1: Repository Acquisition
        print("\n📥 Step 1: Repository Acquisition")
        print("-" * 30)
        
        # Use a small public repository for testing
        repo_url = "https://github.com/octocat/Hello-World.git"
        target_dir = "/tmp/git_agent_test"
        
        acquisition_function = repository_acquisition_agent.tools[0]  # clone_repository_from_url
        acquisition_result = acquisition_function(repo_url, target_dir)
        
        print(f"Repository URL: {repo_url}")
        print(f"Target Directory: {target_dir}")
        
        # Parse the result
        acquisition_data = json.loads(acquisition_result)
        if acquisition_data.get("success"):
            print("✓ Repository cloned successfully")
            print(f"  Local path: {acquisition_data['local_path']}")
            print(f"  Repository name: {acquisition_data['repo_name']}")
        else:
            print("✗ Repository acquisition failed")
            print(f"  Error: {acquisition_data.get('error', 'Unknown error')}")
            return False
        
        # Step 2: Repository Analysis
        print("\n🔍 Step 2: Repository Analysis")
        print("-" * 30)
        
        analysis_function = repository_analysis_agent.tools[0]  # analyze_repository_from_info
        analysis_result = analysis_function(acquisition_result)
        
        # Parse the analysis result
        analysis_data = json.loads(analysis_result)
        if "error" not in analysis_data:
            print("✓ Repository analysis completed successfully")
            
            # Display key metrics
            repo_info = analysis_data.get("repository_info", {})
            commits_info = analysis_data.get("commits", {})
            developers_info = analysis_data.get("developers", {})
            metrics_info = analysis_data.get("metrics", {})
            
            print(f"  Repository path: {repo_info.get('repository_path', 'N/A')}")
            print(f"  Active branch: {repo_info.get('active_branch', 'N/A')}")
            print(f"  Total branches: {repo_info.get('total_branches', 0)}")
            print(f"  Total commits analyzed: {commits_info.get('total_commits_analyzed', 0)}")
            print(f"  Total developers: {developers_info.get('total_developers', 0)}")
            
            if metrics_info:
                commit_metrics = metrics_info.get("commit_metrics", {})
                code_metrics = metrics_info.get("code_metrics", {})
                print(f"  Total insertions: {code_metrics.get('total_insertions', 0)}")
                print(f"  Total deletions: {code_metrics.get('total_deletions', 0)}")
                print(f"  Merge commits: {commit_metrics.get('merge_commits', 0)}")
        else:
            print("✗ Repository analysis failed")
            print(f"  Error: {analysis_data.get('error', 'Unknown error')}")
            return False
        
        # Step 3: Repository Cleanup
        print("\n🧹 Step 3: Repository Cleanup")
        print("-" * 30)
        
        cleanup_function = repository_cleanup_agent.tools[0]  # cleanup_temporary_repository
        cleanup_result = cleanup_function(acquisition_result)
        
        # Parse the cleanup result
        cleanup_data = json.loads(cleanup_result)
        if cleanup_data.get("success"):
            print("✓ Repository cleanup completed successfully")
            print(f"  Cleaned path: {cleanup_data.get('cleaned_path', 'N/A')}")
            cleanup_actions = cleanup_data.get("cleanup_actions", [])
            for action in cleanup_actions:
                print(f"  - {action}")
        else:
            print("✗ Repository cleanup failed")
            print(f"  Error: {cleanup_data.get('error', 'Unknown error')}")
            return False
        
        print("\n🎉 Git Repository Agent Workflow Test Completed Successfully!")
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_agent():
    """Test the main sequential agent."""
    
    print("\n🔧 Testing Main Sequential Agent")
    print("=" * 50)
    
    try:
        from agents.git_repository_agent.agent import root_agent
        
        print("✓ Main agent loaded successfully")
        print(f"  Name: {root_agent.name}")
        print(f"  Sub-agents: {len(root_agent.sub_agents)}")
        
        for i, sub_agent in enumerate(root_agent.sub_agents):
            print(f"    {i+1}. {sub_agent.name} ({len(sub_agent.tools)} tools)")
        
        return True
        
    except Exception as e:
        print(f"✗ Main agent test failed: {e}")
        return False

if __name__ == "__main__":
    print("Git Repository Agent Test Suite")
    print("=" * 50)
    
    # Test individual workflow
    workflow_success = test_git_repository_agent()
    
    # Test main agent
    main_agent_success = test_main_agent()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    print(f"Workflow Test: {'✓ PASSED' if workflow_success else '✗ FAILED'}")
    print(f"Main Agent Test: {'✓ PASSED' if main_agent_success else '✗ FAILED'}")
    
    if workflow_success and main_agent_success:
        print("\n🎉 All tests passed! The Git Repository Agent is ready for use.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        sys.exit(1)
