"""
Enterprise Security Configuration for ADK-Powered Intelligence Platform

This module defines enterprise-grade security policies, guardrails, and configurations
for all agents in the platform, implementing the security framework outlined in the
enterprise requirements.
"""

import os
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Configure security logging
security_logger = logging.getLogger('enterprise_security')
security_logger.setLevel(logging.INFO)

class SecurityLevel(Enum):
    """Security levels for different operations."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class DataClassification(Enum):
    """Data classification levels."""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"

@dataclass
class SecurityPolicy:
    """Security policy configuration."""
    name: str
    level: SecurityLevel
    data_classification: DataClassification
    allowed_domains: List[str]
    blocked_patterns: List[str]
    max_request_size: int
    rate_limit_per_minute: int
    audit_required: bool
    encryption_required: bool

# Enterprise Security Policies
SECURITY_POLICIES = {
    "git_repository_analysis": SecurityPolicy(
        name="Git Repository Analysis",
        level=SecurityLevel.MEDIUM,
        data_classification=DataClassification.INTERNAL,
        allowed_domains=[
            'github.com',
            'gitlab.com',
            'bitbucket.org',
            'dev.azure.com',
            'source.cloud.google.com'
        ],
        blocked_patterns=[
            r'\.\./',  # Path traversal
            r'file://',  # Local file access
            r'ftp://',  # FTP access
            r'ssh://(?!git@)',  # SSH access (except git SSH)
            r'javascript:',  # JavaScript injection
            r'data:',  # Data URLs
        ],
        max_request_size=1000,  # Max commits
        rate_limit_per_minute=10,
        audit_required=True,
        encryption_required=True
    ),
    
    "database_query": SecurityPolicy(
        name="Database Query",
        level=SecurityLevel.HIGH,
        data_classification=DataClassification.CONFIDENTIAL,
        allowed_domains=[],  # Internal only
        blocked_patterns=[
            r'DROP\s+',  # DROP statements
            r'DELETE\s+',  # DELETE statements
            r'UPDATE\s+',  # UPDATE statements
            r'INSERT\s+',  # INSERT statements
            r'ALTER\s+',  # ALTER statements
            r'CREATE\s+',  # CREATE statements
            r'TRUNCATE\s+',  # TRUNCATE statements
            r'EXEC\s*\(',  # EXEC calls
            r'xp_',  # Extended procedures
            r'sp_',  # System procedures
        ],
        max_request_size=10000,  # Max query length
        rate_limit_per_minute=30,
        audit_required=True,
        encryption_required=True
    ),
    
    "api_connector": SecurityPolicy(
        name="API Connector",
        level=SecurityLevel.MEDIUM,
        data_classification=DataClassification.INTERNAL,
        allowed_domains=[
            'api.github.com',
            'api.salesforce.com',
            'api.slack.com',
            'graph.microsoft.com',
            'googleapis.com'
        ],
        blocked_patterns=[
            r'localhost',  # Local access
            r'127\.0\.0\.1',  # Loopback
            r'192\.168\.',  # Private networks
            r'10\.',  # Private networks
            r'172\.(1[6-9]|2[0-9]|3[01])\.',  # Private networks
        ],
        max_request_size=1000000,  # 1MB
        rate_limit_per_minute=100,
        audit_required=True,
        encryption_required=True
    )
}

# Authentication and Authorization Configuration
AUTH_CONFIG = {
    "require_authentication": True,
    "session_timeout_minutes": 60,
    "max_failed_attempts": 3,
    "lockout_duration_minutes": 15,
    "require_mfa": True,
    "allowed_identity_providers": [
        "google",
        "microsoft",
        "okta",
        "auth0"
    ]
}

# Data Protection Configuration
DATA_PROTECTION = {
    "encryption_at_rest": True,
    "encryption_in_transit": True,
    "data_masking_enabled": True,
    "pii_detection_enabled": True,
    "data_retention_days": 90,
    "audit_log_retention_days": 365,
    "backup_encryption": True
}

# Network Security Configuration
NETWORK_SECURITY = {
    "require_tls": True,
    "min_tls_version": "1.2",
    "allowed_cipher_suites": [
        "TLS_AES_256_GCM_SHA384",
        "TLS_CHACHA20_POLY1305_SHA256",
        "TLS_AES_128_GCM_SHA256"
    ],
    "hsts_enabled": True,
    "csrf_protection": True
}

# Monitoring and Alerting Configuration
MONITORING_CONFIG = {
    "security_event_logging": True,
    "real_time_monitoring": True,
    "anomaly_detection": True,
    "alert_thresholds": {
        "failed_auth_attempts": 5,
        "unusual_data_access": 10,
        "high_error_rate": 0.05,
        "resource_exhaustion": 0.8
    },
    "notification_channels": [
        "email",
        "slack",
        "pagerduty"
    ]
}

# Compliance Configuration
COMPLIANCE_CONFIG = {
    "gdpr_compliance": True,
    "ccpa_compliance": True,
    "sox_compliance": True,
    "hipaa_compliance": False,  # Enable if handling health data
    "data_residency_enforcement": True,
    "right_to_be_forgotten": True,
    "consent_management": True
}

def get_security_policy(policy_name: str) -> Optional[SecurityPolicy]:
    """Get security policy by name."""
    return SECURITY_POLICIES.get(policy_name)

def validate_security_compliance(operation: str, **kwargs) -> tuple[bool, str]:
    """
    Validate operation against security policies.
    
    Args:
        operation: Name of the operation to validate
        **kwargs: Operation parameters
        
    Returns:
        Tuple of (is_compliant, error_message)
    """
    policy = get_security_policy(operation)
    if not policy:
        return False, f"No security policy defined for operation: {operation}"
    
    # Log security validation attempt
    security_logger.info(f"Security validation for operation: {operation}")
    
    # Implement validation logic based on policy
    # This would be expanded with specific validation rules
    
    return True, ""

def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "INFO"):
    """
    Log security events for audit trails.
    
    Args:
        event_type: Type of security event
        details: Event details
        severity: Event severity level
    """
    security_logger.log(
        getattr(logging, severity.upper()),
        f"SECURITY_EVENT: {event_type} - {details}"
    )

def get_environment_config() -> Dict[str, Any]:
    """Get environment-specific security configuration."""
    env = os.getenv('ENVIRONMENT', 'development').lower()
    
    if env == 'production':
        return {
            "debug_mode": False,
            "strict_validation": True,
            "enhanced_logging": True,
            "rate_limiting_strict": True
        }
    elif env == 'staging':
        return {
            "debug_mode": False,
            "strict_validation": True,
            "enhanced_logging": True,
            "rate_limiting_strict": False
        }
    else:  # development
        return {
            "debug_mode": True,
            "strict_validation": False,
            "enhanced_logging": False,
            "rate_limiting_strict": False
        }

# Export main configuration
ENTERPRISE_SECURITY_CONFIG = {
    "policies": SECURITY_POLICIES,
    "auth": AUTH_CONFIG,
    "data_protection": DATA_PROTECTION,
    "network": NETWORK_SECURITY,
    "monitoring": MONITORING_CONFIG,
    "compliance": COMPLIANCE_CONFIG,
    "environment": get_environment_config()
}
