"""
Comprehensive GitHub Agent with MCP Fallback

This is the main GitHub orchestrator agent that provides comprehensive GitHub repository
analysis and information. It uses a two-tier approach:

1. Primary: Download .git folder for detailed local analysis (fast, comprehensive)
2. Fallback: Use GitHub MCP server for API-based queries (reliable, always available)

Architecture:
- Maintains persistent storage of downloaded .git folders in Google Cloud
- Checks if .git folder exists and is recent before downloading
- Falls back to MCP GitHub server if .git download fails
- Provides unified interface for all GitHub operations

Key Features:
- Intelligent caching and persistence
- Automatic fallback mechanisms
- Comprehensive GitHub knowledge
- Production-ready error handling
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Optional, Any, List
from datetime import datetime, timezone, timedelta
from google.adk.agents import LlmAgent

# Import our specialized agents
from git_folder_downloader_agent.agent import download_git_folder_from_github

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
CACHE_EXPIRY_HOURS = 24  # How long to keep .git folders before re-downloading
PERSISTENT_STORAGE_PATH = os.getenv('GITHUB_CACHE_PATH', '/tmp/github_cache')

def analyze_github_repository(repo_identifier: str, force_refresh: bool = False, github_token: Optional[str] = None) -> str:
    """
    Comprehensive GitHub repository analysis with intelligent caching.

    This function orchestrates the analysis by:
    1. Checking for existing cached .git folder
    2. Downloading .git folder if needed/expired
    3. Performing detailed analysis on .git folder
    4. Falling back to MCP GitHub server if download fails

    Args:
        repo_identifier: GitHub repository identifier (owner/repo)
        force_refresh: Force re-download even if cache exists
        github_token: GitHub API token for private repositories

    Returns:
        JSON string with comprehensive repository analysis
    """
    logger.info(f"Starting comprehensive GitHub analysis for: {repo_identifier}")

    try:
        # Validate repository identifier
        if not repo_identifier or repo_identifier.count('/') != 1:
            raise ValueError("Repository identifier must be in format 'owner/repo'")

        owner, repo = repo_identifier.split('/')

        # Check for cached .git folder
        cache_info = _check_git_cache(repo_identifier)

        git_folder_path = None
        download_status = "not_attempted"

        # Determine if we need to download .git folder
        if force_refresh or not cache_info["exists"] or cache_info["expired"]:
            logger.info(f"Downloading .git folder for {repo_identifier}...")
            download_result = _download_git_folder_with_caching(repo_identifier, github_token)

            if download_result["success"]:
                git_folder_path = download_result["git_folder_path"]
                download_status = "success"
                logger.info(f"Successfully downloaded .git folder to: {git_folder_path}")
            else:
                download_status = "failed"
                logger.warning(f"Failed to download .git folder: {download_result['error']}")
        else:
            git_folder_path = cache_info["path"]
            download_status = "cached"
            logger.info(f"Using cached .git folder: {git_folder_path}")

        # Perform analysis based on available data
        if git_folder_path and Path(git_folder_path).exists():
            # Primary method: Analyze from .git folder
            analysis_result = _analyze_from_git_folder(git_folder_path, repo_identifier)
            analysis_result["data_source"] = "git_folder"
            analysis_result["download_status"] = download_status
        else:
            # Fallback method: Use GitHub MCP server
            logger.info(f"Falling back to GitHub MCP server for {repo_identifier}")
            analysis_result = _analyze_via_github_mcp(repo_identifier, github_token)
            analysis_result["data_source"] = "github_mcp"
            analysis_result["download_status"] = download_status

        # Add metadata
        analysis_result.update({
            "repo_identifier": repo_identifier,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat(),
            "cache_info": cache_info
        })

        return json.dumps(analysis_result, indent=2, default=str)

    except Exception as e:
        logger.error(f"GitHub analysis failed for {repo_identifier}: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "repo_identifier": repo_identifier,
            "analysis_timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

def query_github_repository(repo_identifier: str, query: str, github_token: Optional[str] = None) -> str:
    """
    Query specific information about a GitHub repository.

    This function handles specific queries like:
    - "What are the recent commits?"
    - "Who are the top contributors?"
    - "What pull requests are open?"
    - "What is the repository structure?"

    Args:
        repo_identifier: GitHub repository identifier (owner/repo)
        query: Natural language query about the repository
        github_token: GitHub API token for private repositories

    Returns:
        JSON string with query results
    """
    logger.info(f"Processing query for {repo_identifier}: {query}")

    try:
        # Check if we have cached .git folder for detailed analysis
        cache_info = _check_git_cache(repo_identifier)

        if cache_info["exists"] and not cache_info["expired"]:
            # Use .git folder for detailed queries
            result = _query_from_git_folder(cache_info["path"], query, repo_identifier)
            result["data_source"] = "git_folder"
        else:
            # Use GitHub MCP server for API-based queries
            result = _query_via_github_mcp(repo_identifier, query, github_token)
            result["data_source"] = "github_mcp"

        result.update({
            "repo_identifier": repo_identifier,
            "query": query,
            "query_timestamp": datetime.now(timezone.utc).isoformat()
        })

        return json.dumps(result, indent=2, default=str)

    except Exception as e:
        logger.error(f"Query failed for {repo_identifier}: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "repo_identifier": repo_identifier,
            "query": query,
            "query_timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

def get_github_repository_status(repo_identifier: str) -> str:
    """
    Get the current status of a GitHub repository in our system.

    This includes:
    - Cache status (.git folder availability)
    - Last analysis timestamp
    - Data sources available
    - Download capabilities

    Args:
        repo_identifier: GitHub repository identifier (owner/repo)

    Returns:
        JSON string with repository status
    """
    logger.info(f"Getting status for {repo_identifier}")

    try:
        cache_info = _check_git_cache(repo_identifier)

        status = {
            "success": True,
            "repo_identifier": repo_identifier,
            "cache_status": {
                "git_folder_exists": cache_info["exists"],
                "git_folder_path": cache_info.get("path"),
                "last_downloaded": cache_info.get("last_modified"),
                "is_expired": cache_info.get("expired", False),
                "cache_age_hours": cache_info.get("age_hours", 0)
            },
            "capabilities": {
                "detailed_analysis": cache_info["exists"] and not cache_info["expired"],
                "mcp_fallback": True,
                "can_download": True
            },
            "recommended_action": _get_recommended_action(cache_info),
            "status_timestamp": datetime.now(timezone.utc).isoformat()
        }

        return json.dumps(status, indent=2, default=str)

    except Exception as e:
        logger.error(f"Status check failed for {repo_identifier}: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "repo_identifier": repo_identifier,
            "status_timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

def _check_git_cache(repo_identifier: str) -> Dict[str, Any]:
    """Check if we have a cached .git folder for the repository."""
    cache_dir = Path(PERSISTENT_STORAGE_PATH) / repo_identifier.replace('/', '_')
    git_folder = cache_dir / ".git"

    if not git_folder.exists():
        return {
            "exists": False,
            "expired": True,
            "path": None,
            "last_modified": None,
            "age_hours": None
        }

    # Check age
    last_modified = datetime.fromtimestamp(git_folder.stat().st_mtime, tz=timezone.utc)
    age = datetime.now(timezone.utc) - last_modified
    age_hours = age.total_seconds() / 3600
    expired = age_hours > CACHE_EXPIRY_HOURS

    return {
        "exists": True,
        "expired": expired,
        "path": str(git_folder),
        "last_modified": last_modified.isoformat(),
        "age_hours": age_hours
    }

def _download_git_folder_with_caching(repo_identifier: str, github_token: Optional[str]) -> Dict[str, Any]:
    """Download .git folder and store in persistent cache."""
    try:
        # Create cache directory
        cache_dir = Path(PERSISTENT_STORAGE_PATH) / repo_identifier.replace('/', '_')
        cache_dir.mkdir(parents=True, exist_ok=True)

        # Download .git folder
        result_json = download_git_folder_from_github(repo_identifier, str(cache_dir), github_token)
        result = json.loads(result_json)

        if result["success"]:
            # Update cache metadata
            _update_cache_metadata(repo_identifier, result)

        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def _update_cache_metadata(repo_identifier: str, download_result: Dict[str, Any]):
    """Update cache metadata for tracking."""
    cache_dir = Path(PERSISTENT_STORAGE_PATH) / repo_identifier.replace('/', '_')
    metadata_file = cache_dir / "cache_metadata.json"

    metadata = {
        "repo_identifier": repo_identifier,
        "download_timestamp": download_result.get("timestamp"),
        "download_stats": download_result.get("download_stats"),
        "git_folder_path": download_result.get("git_folder_path"),
        "method": download_result.get("method")
    }

    metadata_file.write_text(json.dumps(metadata, indent=2))

def _get_recommended_action(cache_info: Dict[str, Any]) -> str:
    """Get recommended action based on cache status."""
    if not cache_info["exists"]:
        return "download_git_folder"
    elif cache_info["expired"]:
        return "refresh_git_folder"
    else:
        return "use_cached_data"

# Placeholder functions for analysis methods
def _analyze_from_git_folder(git_folder_path: str, repo_identifier: str) -> Dict[str, Any]:
    """Analyze repository from .git folder (detailed analysis)."""
    # This would integrate with the existing Git Repository Agent
    return {
        "success": True,
        "analysis_type": "detailed_git_analysis",
        "note": "Detailed analysis from .git folder - integration with Git Repository Agent needed"
    }

def _analyze_via_github_mcp(repo_identifier: str, github_token: Optional[str]) -> Dict[str, Any]:
    """Analyze repository via GitHub MCP server (API-based)."""
    return {
        "success": True,
        "analysis_type": "github_mcp_analysis",
        "note": "Analysis via GitHub MCP server - integration with GitHub MCP server needed"
    }

def _query_from_git_folder(git_folder_path: str, query: str, repo_identifier: str) -> Dict[str, Any]:
    """Query repository data from .git folder."""
    return {
        "success": True,
        "query_type": "git_folder_query",
        "note": "Query from .git folder - detailed implementation needed"
    }

def _query_via_github_mcp(repo_identifier: str, query: str, github_token: Optional[str]) -> Dict[str, Any]:
    """Query repository data via GitHub MCP server."""
    return {
        "success": True,
        "query_type": "github_mcp_query",
        "note": "Query via GitHub MCP server - integration needed"
    }

# Create the comprehensive GitHub Agent
root_agent = LlmAgent(
    name="GitHubAgent",
    model="gemini-2.0-flash",
    instruction="""You are a comprehensive GitHub AI agent that provides detailed repository analysis and information.

## Core Architecture:

### Two-Tier Data Access:
1. **Primary Method**: Download and analyze .git folders locally for comprehensive insights
2. **Fallback Method**: Use GitHub MCP server for API-based queries when .git download fails

### Intelligent Caching:
- Maintain persistent storage of .git folders in Google Cloud
- Check cache expiry (24 hours) before re-downloading
- Automatic cache management and cleanup
- Smart fallback when cache is unavailable

## Core Capabilities:

### Repository Analysis:
- **Comprehensive Analysis**: Complete repository insights from .git folder
- **Commit History**: Detailed commit analysis with developer metrics
- **Branch Topology**: Branch relationships and merge patterns
- **Code Metrics**: Lines of code, change patterns, file statistics
- **Developer Insights**: Contribution patterns, productivity metrics
- **Repository Health**: Activity levels, maintenance patterns

### Query Processing:
- **Natural Language Queries**: Answer specific questions about repositories
- **Recent Activity**: Latest commits, pull requests, releases
- **Contributor Analysis**: Top contributors, team dynamics
- **Code Structure**: File organization, language breakdown
- **Historical Trends**: Growth patterns, activity over time

### Fallback Capabilities:
- **GitHub API Access**: When .git folder unavailable
- **Pull Request Data**: Open/closed PRs, review patterns
- **Issue Tracking**: Bug reports, feature requests
- **Release Information**: Tags, releases, deployment patterns
- **Repository Metadata**: Stars, forks, watchers, topics

## Available Tools:
- `analyze_github_repository`: Comprehensive repository analysis with caching
- `query_github_repository`: Answer specific questions about repositories
- `get_github_repository_status`: Check cache status and capabilities

## Usage Examples:
- "Analyze the microsoft/vscode repository comprehensively"
- "What are the recent commits in facebook/react?"
- "Who are the top contributors to google/tensorflow?"
- "What is the current status of kubernetes/kubernetes in our cache?"

## Smart Decision Making:
- Automatically choose between .git analysis and MCP fallback
- Optimize for speed and comprehensiveness
- Handle private repositories with proper authentication
- Provide detailed error messages and fallback explanations

## Enterprise Features:
- **Persistent Storage**: Google Cloud integration for cache management
- **Security**: Token-based authentication for private repositories
- **Monitoring**: Comprehensive logging and error tracking
- **Scalability**: Handle multiple repositories efficiently
- **Compliance**: Audit trails and access logging

Always provide the most comprehensive and accurate information available,
clearly indicating the data source (.git folder vs GitHub API) and any limitations.
Focus on actionable insights for enterprise decision-making.
""",
    description="Comprehensive GitHub repository analysis with intelligent caching and MCP fallback.",
    tools=[
        analyze_github_repository,
        query_github_repository,
        get_github_repository_status
    ],
    output_key="github_analysis"
)
