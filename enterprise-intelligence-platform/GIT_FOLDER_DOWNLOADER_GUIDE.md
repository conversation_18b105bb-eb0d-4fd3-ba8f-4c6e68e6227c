# Git Folder Downloader Agent - Efficient .git Folder Download

## Overview

The Git Folder Downloader Agent is a specialized MCP agent that efficiently downloads only the `.git` folder from repositories using APIs instead of full repository cloning. This approach provides significant advantages for Git repository analysis in enterprise environments.

## 🎯 **Key Benefits**

### **Efficiency Advantages**
- **Faster Downloads**: Only downloads Git metadata, not file contents
- **Reduced Bandwidth**: Minimal data transfer compared to full cloning
- **Lower Storage**: No working directory files stored locally
- **No Cleanup Required**: No temporary files to manage

### **Security Benefits**
- **API-Based**: Uses secure API tokens instead of Git protocol
- **Controlled Access**: Leverages existing repository permissions
- **Audit Trail**: All API calls are logged and traceable
- **No Git Vulnerabilities**: Avoids potential Git protocol security issues

### **Enterprise Features**
- **Token Authentication**: Supports GitHub, GitLab, and other API tokens
- **Rate Limiting**: Respects API rate limits automatically
- **Error Handling**: Graceful failure with detailed error messages
- **Scalability**: Can handle large repositories efficiently

## 🔧 **Available Tools**

### 1. `download_git_folder_from_github`
Downloads .git folder from GitHub repositories using GitHub API.

**Parameters:**
- `repo_identifier` (str): Repository in format "owner/repo" (e.g., "microsoft/vscode")
- `target_dir` (Optional[str]): Target directory (creates temp dir if not provided)
- `github_token` (Optional[str]): GitHub API token (uses GITHUB_TOKEN env var if not provided)

**Example:**
```python
result = download_git_folder_from_github("microsoft/vscode", "/tmp/analysis", "ghp_token123")
```

### 2. `download_git_folder_from_gitlab`
Downloads .git folder from GitLab repositories using GitLab API.

**Parameters:**
- `repo_identifier` (str): Repository in format "owner/repo"
- `target_dir` (Optional[str]): Target directory
- `gitlab_token` (Optional[str]): GitLab API token (uses GITLAB_TOKEN env var if not provided)
- `gitlab_url` (str): GitLab instance URL (default: "https://gitlab.com")

**Example:**
```python
result = download_git_folder_from_gitlab("group/project", "/tmp/analysis", "glpat-token123")
```

### 3. `download_git_folder_from_url`
Auto-detects Git provider and downloads .git folder from any supported Git URL.

**Parameters:**
- `git_url` (str): Full Git repository URL
- `target_dir` (Optional[str]): Target directory
- `auth_token` (Optional[str]): Authentication token

**Example:**
```python
result = download_git_folder_from_url("https://github.com/facebook/react.git", "/tmp/analysis", "token123")
```

## 🔐 **Authentication Setup**

### GitHub Authentication
```bash
# Set environment variable
export GITHUB_TOKEN="ghp_your_token_here"

# Or pass directly to function
download_git_folder_from_github("owner/repo", github_token="ghp_your_token_here")
```

### GitLab Authentication
```bash
# Set environment variable
export GITLAB_TOKEN="glpat_your_token_here"

# Or pass directly to function
download_git_folder_from_gitlab("owner/repo", gitlab_token="glpat_your_token_here")
```

## 📁 **Output Structure**

The agent creates a complete `.git` folder structure:

```
target_directory/
└── .git/
    ├── HEAD                    # Current branch reference
    ├── config                  # Repository configuration
    ├── index                   # Staging area (empty)
    ├── objects/                # Git objects
    │   ├── ab/                 # Object directories (first 2 chars of SHA)
    │   │   └── cdef123...      # Object files
    │   └── ...
    ├── refs/
    │   ├── heads/              # Local branch references
    │   │   ├── main
    │   │   └── develop
    │   ├── tags/               # Tag references
    │   │   └── v1.0.0
    │   └── remotes/
    │       └── origin/         # Remote tracking branches
    │           ├── main
    │           └── develop
    └── logs/                   # Reference logs
        └── refs/
```

## 📊 **Response Format**

### Successful Download
```json
{
  "success": true,
  "repo_identifier": "microsoft/vscode",
  "git_folder_path": "/tmp/analysis/.git",
  "target_directory": "/tmp/analysis",
  "download_stats": {
    "refs_downloaded": 25,
    "objects_downloaded": 100,
    "commits_processed": 100,
    "total_size_bytes": 1048576
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": "GitHub token is required. Set GITHUB_TOKEN environment variable",
  "repo_identifier": "microsoft/vscode",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🚀 **Usage Examples**

### Basic GitHub Download
```python
# Download Microsoft VS Code .git folder
result = download_git_folder_from_github("microsoft/vscode")
print(f"Downloaded to: {json.loads(result)['git_folder_path']}")
```

### Enterprise GitLab Download
```python
# Download from enterprise GitLab instance
result = download_git_folder_from_gitlab(
    "enterprise/project",
    target_dir="/analysis/workspace",
    gitlab_token="glpat_enterprise_token",
    gitlab_url="https://gitlab.company.com"
)
```

### Auto-Detection from URL
```python
# Let the agent detect the provider
result = download_git_folder_from_url(
    "https://github.com/facebook/react.git",
    auth_token="ghp_token123"
)
```

## 🔍 **Integration with Analysis Agents**

The downloaded `.git` folder can be directly used by Git analysis agents:

```python
# 1. Download .git folder
download_result = download_git_folder_from_github("microsoft/vscode")
git_path = json.loads(download_result)["git_folder_path"]

# 2. Analyze the .git folder
analysis_result = analyze_git_repository(git_path.replace("/.git", ""))
```

## ⚡ **Performance Comparison**

| Method | Time | Bandwidth | Storage | Cleanup |
|--------|------|-----------|---------|---------|
| **Full Clone** | 30-60s | 100-500MB | 100-500MB | Required |
| **Shallow Clone** | 15-30s | 50-100MB | 50-100MB | Required |
| **API Download** | 5-15s | 1-10MB | 1-10MB | None |

## 🛡️ **Security Features**

- **Token-based Authentication**: Secure API access
- **Input Validation**: Repository identifier validation
- **Error Handling**: No sensitive information in error messages
- **Rate Limiting**: Respects API rate limits
- **Audit Logging**: All operations logged for compliance

## 🔧 **Configuration**

### Environment Variables
```bash
# GitHub
export GITHUB_TOKEN="ghp_your_github_token"

# GitLab
export GITLAB_TOKEN="glpat_your_gitlab_token"

# Optional: Logging level
export LOG_LEVEL="INFO"
```

### ADK Integration
The agent is automatically available in ADK web interface:
- Agent Name: `GitFolderDownloader`
- Available at: http://localhost:8000
- Select from agent dropdown in web UI

## 🎯 **Use Cases**

1. **Repository Analysis**: Download .git for commit history analysis
2. **Developer Metrics**: Extract contributor statistics
3. **Code Quality Assessment**: Analyze change patterns
4. **Compliance Auditing**: Review commit signatures and history
5. **DORA Metrics**: Calculate deployment frequency and lead time

## 🚨 **Limitations**

- **Recent Commits Only**: Downloads last 100 commits for efficiency
- **No File Contents**: Only Git metadata, not actual file contents
- **API Rate Limits**: Subject to GitHub/GitLab API rate limits
- **Token Required**: Requires valid API tokens for private repositories

## 🔄 **Future Enhancements**

- **Bitbucket Support**: Add Bitbucket API integration
- **Azure DevOps**: Support for Azure Repos
- **Incremental Updates**: Update existing .git folders
- **Compression**: Compress downloaded objects
- **Parallel Downloads**: Concurrent object downloads

This Git Folder Downloader Agent provides an efficient, secure, and scalable solution for Git repository analysis in enterprise environments.
