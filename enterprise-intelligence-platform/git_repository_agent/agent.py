"""
Git Repository Agent for ADK Web Interface

This is a simplified version of the Git Repository Agent that can be easily
accessed through the ADK web interface for testing and demonstration.
"""

import json
import tempfile
import shutil
import os
import re
import logging
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
from urllib.parse import urlparse

import git
from git import Repo, Commit
from google.adk.agents import LlmAgent

# Configure logging for security audit trails
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# --- Constants ---
GEMINI_MODEL = "gemini-2.0-flash"

# Security Configuration
MAX_COMMITS_LIMIT = 1000  # Hard limit for security
ALLOWED_DOMAINS = [
    'github.com',
    'gitlab.com',
    'bitbucket.org',
    'dev.azure.com',
    'source.cloud.google.com'
]
BLOCKED_PATTERNS = [
    r'\.\./',  # Path traversal
    r'file://',  # Local file access
    r'ftp://',  # FTP access
    r'ssh://',  # SSH access (unless explicitly allowed)
]

def validate_repository_url(repo_url: str) -> tuple[bool, str]:
    """
    Validate repository URL for security compliance.

    Args:
        repo_url: Repository URL to validate

    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Basic input validation
        if not repo_url or not isinstance(repo_url, str):
            return False, "Repository URL must be a non-empty string"

        # Check for blocked patterns
        for pattern in BLOCKED_PATTERNS:
            if re.search(pattern, repo_url, re.IGNORECASE):
                logger.warning(f"Blocked pattern detected in URL: {repo_url}")
                return False, f"URL contains blocked pattern: {pattern}"

        # Parse URL
        parsed = urlparse(repo_url)

        # Validate scheme
        if parsed.scheme not in ['https', 'http']:
            return False, "Only HTTP/HTTPS URLs are allowed"

        # Validate domain (if domain restrictions are enabled)
        if ALLOWED_DOMAINS and parsed.netloc not in ALLOWED_DOMAINS:
            return False, f"Domain {parsed.netloc} is not in allowed domains list"

        # Additional security checks
        if len(repo_url) > 2048:  # Reasonable URL length limit
            return False, "URL exceeds maximum length"

        logger.info(f"Repository URL validation passed: {repo_url}")
        return True, ""

    except Exception as e:
        logger.error(f"URL validation error: {str(e)}")
        return False, f"URL validation failed: {str(e)}"

def validate_max_commits(max_commits: int) -> tuple[bool, str]:
    """
    Validate max_commits parameter for security compliance.

    Args:
        max_commits: Maximum number of commits to analyze

    Returns:
        Tuple of (is_valid, error_message)
    """
    if not isinstance(max_commits, int):
        return False, "max_commits must be an integer"

    if max_commits < 1:
        return False, "max_commits must be at least 1"

    if max_commits > MAX_COMMITS_LIMIT:
        return False, f"max_commits exceeds security limit of {MAX_COMMITS_LIMIT}"

    return True, ""

@dataclass
class CommitInfo:
    """Structured commit information."""
    sha: str
    message: str
    author_name: str
    author_email: str
    committer_name: str
    committer_email: str
    authored_date: datetime
    committed_date: datetime
    parents: List[str]
    files_changed: List[str]
    insertions: int
    deletions: int
    total_changes: int
    is_merge: bool
    branch: Optional[str] = None

def clone_and_analyze_repository(repo_url: str, max_commits: int = 100) -> str:
    """
    Clone a Git repository and perform comprehensive analysis with security validation.

    Args:
        repo_url: URL of the Git repository to clone and analyze
        max_commits: Maximum number of commits to analyze (default: 100)

    Returns:
        JSON string with comprehensive repository analysis
    """
    # Security audit logging
    logger.info(f"Repository analysis request - URL: {repo_url}, max_commits: {max_commits}")

    # Input validation and security checks
    url_valid, url_error = validate_repository_url(repo_url)
    if not url_valid:
        logger.warning(f"Repository URL validation failed: {url_error}")
        return json.dumps({
            "error": f"Security validation failed: {url_error}",
            "repo_url": repo_url,
            "timestamp": datetime.now().isoformat()
        }, indent=2)

    commits_valid, commits_error = validate_max_commits(max_commits)
    if not commits_valid:
        logger.warning(f"Max commits validation failed: {commits_error}")
        return json.dumps({
            "error": f"Parameter validation failed: {commits_error}",
            "max_commits": max_commits,
            "timestamp": datetime.now().isoformat()
        }, indent=2)

    temp_dir = None
    try:
        # Create temporary directory with secure permissions
        temp_dir = tempfile.mkdtemp(prefix="git_analysis_", dir="/tmp")
        os.chmod(temp_dir, 0o700)  # Restrict access to owner only

        repo_name = repo_url.split('/')[-1].replace('.git', '')
        # Sanitize repo name to prevent path traversal
        repo_name = re.sub(r'[^a-zA-Z0-9_-]', '_', repo_name)
        local_path = os.path.join(temp_dir, repo_name)

        # Clone repository with timeout and security settings
        logger.info(f"Cloning repository {repo_url} to {local_path}...")
        Repo.clone_from(repo_url, local_path, depth=1)  # Shallow clone for security

        # Analyze repository
        analysis = analyze_git_repository(local_path, max_commits)

        # Add security and audit information
        analysis["security_info"] = {
            "validation_passed": True,
            "max_commits_requested": max_commits,
            "max_commits_limit": MAX_COMMITS_LIMIT,
            "allowed_domains": ALLOWED_DOMAINS,
            "analysis_timestamp": datetime.now().isoformat()
        }

        analysis["clone_info"] = {
            "repo_url": repo_url,
            "local_path": local_path,
            "repo_name": repo_name,
            "clone_timestamp": datetime.now().isoformat(),
            "clone_method": "shallow_clone"
        }

        # Security audit log for successful analysis
        logger.info(f"Repository analysis completed successfully - URL: {repo_url}, commits: {analysis.get('commits', {}).get('total_commits_analyzed', 0)}")

        return json.dumps(analysis, indent=2, default=str)

    except Exception as e:
        error_msg = f"Failed to clone and analyze repository: {str(e)}"
        logger.error(f"Repository analysis failed - URL: {repo_url}, Error: {error_msg}")
        return json.dumps({
            "error": error_msg,
            "repo_url": repo_url,
            "timestamp": datetime.now().isoformat(),
            "security_info": {
                "validation_passed": True,
                "error_during_processing": True
            }
        }, indent=2)
    finally:
        # Secure cleanup of temporary directory
        if temp_dir and os.path.exists(temp_dir):
            try:
                # Secure deletion of temporary files
                shutil.rmtree(temp_dir)
                logger.info(f"Temporary directory cleaned up: {temp_dir}")
            except Exception as e:
                logger.error(f"Failed to cleanup temporary directory {temp_dir}: {e}")

def analyze_git_repository(repo_path: str, max_commits: int = 100) -> Dict[str, Any]:
    """
    Analyze a Git repository by reading directly from the .git folder.

    Args:
        repo_path: Path to the Git repository
        max_commits: Maximum number of commits to analyze

    Returns:
        Comprehensive repository analysis data
    """
    try:
        repo = Repo(repo_path)

        # Basic repository information
        repo_info = {
            "repository_path": repo_path,
            "is_bare": repo.bare,
            "head_commit": str(repo.head.commit.hexsha) if not repo.bare else None,
            "active_branch": str(repo.active_branch) if not repo.bare and not repo.head.is_detached else None,
            "total_branches": len(list(repo.branches)),
            "total_tags": len(list(repo.tags)),
            "analysis_timestamp": datetime.now().isoformat()
        }

        # Analyze commits
        commits_data = analyze_commits(repo, max_commits)

        # Analyze developers
        developers_data = analyze_developers(commits_data["commits"])

        # Analyze branches
        branches_data = analyze_branches(repo)

        # Generate repository metrics
        metrics_data = generate_repository_metrics(commits_data, developers_data)

        return {
            "repository_info": repo_info,
            "commits": commits_data,
            "developers": developers_data,
            "branches": branches_data,
            "metrics": metrics_data
        }

    except Exception as e:
        return {"error": f"Failed to analyze repository: {str(e)}"}

def analyze_commits(repo: Repo, max_commits: int = 100) -> Dict[str, Any]:
    """Analyze commit history from the repository."""
    commits = []
    commit_count = 0

    try:
        # Get all commits from all branches
        for commit in repo.iter_commits('--all', max_count=max_commits):
            commit_info = extract_commit_info(commit)
            commits.append(asdict(commit_info))
            commit_count += 1

        # Sort commits by date (newest first)
        commits.sort(key=lambda x: x['committed_date'], reverse=True)

        return {
            "total_commits_analyzed": commit_count,
            "commits": commits,
            "date_range": {
                "earliest": commits[-1]['committed_date'] if commits else None,
                "latest": commits[0]['committed_date'] if commits else None
            }
        }

    except Exception as e:
        return {"error": f"Failed to analyze commits: {str(e)}", "commits": []}

def extract_commit_info(commit: Commit) -> CommitInfo:
    """Extract detailed information from a Git commit."""
    # Get file statistics
    stats = commit.stats
    files_changed = list(stats.files.keys())
    insertions = stats.total['insertions']
    deletions = stats.total['deletions']

    # Check if it's a merge commit
    is_merge = len(commit.parents) > 1

    return CommitInfo(
        sha=commit.hexsha,
        message=commit.message.strip(),
        author_name=commit.author.name,
        author_email=commit.author.email,
        committer_name=commit.committer.name,
        committer_email=commit.committer.email,
        authored_date=commit.authored_datetime,
        committed_date=commit.committed_datetime,
        parents=[parent.hexsha for parent in commit.parents],
        files_changed=files_changed,
        insertions=insertions,
        deletions=deletions,
        total_changes=insertions + deletions,
        is_merge=is_merge
    )

def analyze_developers(commits: List[Dict]) -> Dict[str, Any]:
    """Analyze developer contributions and metrics."""
    developers = defaultdict(lambda: {
        'commits': [],
        'total_insertions': 0,
        'total_deletions': 0,
        'files_touched': set(),
        'merge_commits': 0
    })

    # Aggregate developer data
    for commit_data in commits:
        author_key = f"{commit_data['author_name']} <{commit_data['author_email']}>"
        dev_data = developers[author_key]

        dev_data['commits'].append(commit_data)
        dev_data['total_insertions'] += commit_data['insertions']
        dev_data['total_deletions'] += commit_data['deletions']
        dev_data['files_touched'].update(commit_data['files_changed'])

        if commit_data['is_merge']:
            dev_data['merge_commits'] += 1

    # Generate developer metrics
    developer_metrics = []
    for author_key, dev_data in developers.items():
        if dev_data['commits']:  # Only include developers with commits
            commits_dates = []
            for c in dev_data['commits']:
                if isinstance(c['committed_date'], str):
                    commits_dates.append(datetime.fromisoformat(c['committed_date'].replace('Z', '+00:00')))
                else:
                    commits_dates.append(c['committed_date'])

            metrics = {
                "name": dev_data['commits'][0]['author_name'],
                "email": dev_data['commits'][0]['author_email'],
                "total_commits": len(dev_data['commits']),
                "total_insertions": dev_data['total_insertions'],
                "total_deletions": dev_data['total_deletions'],
                "first_commit_date": min(commits_dates),
                "last_commit_date": max(commits_dates),
                "active_days": len(set(d.date() for d in commits_dates)),
                "files_touched": len(dev_data['files_touched']),
                "merge_commits": dev_data['merge_commits'],
                "avg_commit_size": (dev_data['total_insertions'] + dev_data['total_deletions']) / len(dev_data['commits'])
            }

            developer_metrics.append(metrics)

    # Sort by total commits (most active first)
    developer_metrics.sort(key=lambda x: x['total_commits'], reverse=True)

    return {
        "total_developers": len(developer_metrics),
        "developers": developer_metrics
    }

def analyze_branches(repo: Repo) -> Dict[str, Any]:
    """Analyze branch information."""
    branches = []

    try:
        for branch in repo.branches:
            branch_info = {
                "name": branch.name,
                "commit_sha": branch.commit.hexsha,
                "commit_message": branch.commit.message.strip(),
                "last_commit_date": branch.commit.committed_datetime.isoformat(),
                "author": branch.commit.author.name,
                "is_active": branch == repo.active_branch if not repo.head.is_detached else False
            }
            branches.append(branch_info)
    except Exception as e:
        return {"error": f"Failed to analyze branches: {str(e)}", "branches": []}

    return {
        "total_branches": len(branches),
        "branches": branches
    }

def generate_repository_metrics(commits_data: Dict, developers_data: Dict) -> Dict[str, Any]:
    """Generate high-level repository metrics."""
    commits = commits_data.get('commits', [])
    developers = developers_data.get('developers', [])

    if not commits:
        return {"error": "No commits data available for metrics"}

    # Calculate time-based metrics
    now = datetime.now(timezone.utc)
    last_30_days = now - timedelta(days=30)
    last_7_days = now - timedelta(days=7)

    recent_commits_30d = []
    recent_commits_7d = []

    for c in commits:
        if isinstance(c['committed_date'], str):
            commit_date = datetime.fromisoformat(c['committed_date'].replace('Z', '+00:00'))
        else:
            commit_date = c['committed_date']

        if commit_date > last_30_days:
            recent_commits_30d.append(c)
        if commit_date > last_7_days:
            recent_commits_7d.append(c)

    # Calculate code metrics
    total_insertions = sum(c['insertions'] for c in commits)
    total_deletions = sum(c['deletions'] for c in commits)

    return {
        "commit_metrics": {
            "total_commits": len(commits),
            "commits_last_30_days": len(recent_commits_30d),
            "commits_last_7_days": len(recent_commits_7d),
            "merge_commits": len([c for c in commits if c['is_merge']]),
            "avg_commits_per_day": len(commits) / max(1, (now - (datetime.fromisoformat(commits[-1]['committed_date'].replace('Z', '+00:00')) if isinstance(commits[-1]['committed_date'], str) else commits[-1]['committed_date'])).days)
        },
        "code_metrics": {
            "total_insertions": total_insertions,
            "total_deletions": total_deletions,
            "net_lines_added": total_insertions - total_deletions,
            "avg_commit_size": (total_insertions + total_deletions) / len(commits) if commits else 0
        },
        "developer_metrics": {
            "total_developers": len(developers),
            "active_developers_30d": len([d for d in developers if (datetime.fromisoformat(d['last_commit_date'].replace('Z', '+00:00')) if isinstance(d['last_commit_date'], str) else d['last_commit_date']) > last_30_days]),
            "top_contributor": developers[0]['name'] if developers else None,
            "avg_commits_per_developer": sum(d['total_commits'] for d in developers) / len(developers) if developers else 0
        }
    }

# Create the Git Repository Agent for ADK Web
root_agent = LlmAgent(
    name="GitRepositoryAgent",
    model=GEMINI_MODEL,
    instruction="""You are a Git Repository Analysis AI specialized in comprehensive Git repository analysis.

Your role is to analyze Git repositories by cloning them and extracting comprehensive insights for enterprise intelligence.

## Core Responsibilities:

### Repository Analysis:
- Clone Git repositories from URLs
- Analyze complete commit history with detailed metadata
- Extract file changes, insertions, deletions for each commit
- Identify merge commits and infer pull request patterns
- Calculate developer contribution metrics and activity patterns
- Track branch relationships and repository topology
- Generate repository health and activity metrics

### Data Extraction:
- Process commits for comprehensive analysis (configurable limit)
- Extract developer productivity metrics and collaboration patterns
- Identify code change patterns and repository hotspots
- Calculate time-based activity metrics (last 7/30 days)
- Generate DORA metrics foundation data

### Output Requirements:
- Provide structured JSON with comprehensive analysis results
- Include repository info, commits, developers, branches, and metrics
- Focus on actionable insights for business intelligence
- Ensure data is ready for consumption by other agents

## Available Tools:
- `clone_and_analyze_repository`: Clone and analyze a Git repository from URL

## Use Cases:
- Developer productivity analysis and team insights
- Code review process optimization and patterns
- Repository health monitoring and maintenance planning
- DORA metrics calculation foundation
- Technical debt identification through change patterns
- Collaboration pattern analysis and team dynamics

Always focus on providing actionable insights that support enterprise intelligence
and decision-making processes. Ensure your analysis is comprehensive yet efficient.

## Example Usage:
- "Analyze the repository https://github.com/microsoft/vscode"
- "Clone and analyze https://github.com/facebook/react for developer insights"
- "Provide metrics for https://github.com/google/tensorflow repository"
""",
    description="Analyzes Git repositories by cloning and reading .git folder for comprehensive insights.",
    tools=[clone_and_analyze_repository],
    output_key="analysis_results"
)
