"""
GitHub .git Folder Downloader Agent

Production-ready agent for downloading only the .git folder from GitHub repositories.
This agent provides efficient access to Git metadata without downloading file contents.

Key Features:
- GitHub-only focus for maximum reliability
- Shallow clone + extract method
- Automatic cleanup and error handling
- Production logging and monitoring
- Google Cloud storage integration ready
"""

import os
import json
import tempfile
import logging
import shutil
import subprocess
from pathlib import Path
from typing import Dict, Optional, Any
from datetime import datetime, timezone
from google.adk.agents import LlmAgent

# Configure production logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def download_git_folder_from_github(repo_identifier: str, target_dir: Optional[str] = None, github_token: Optional[str] = None) -> str:
    """
    Download only the .git folder from a GitHub repository using shallow clone.

    Args:
        repo_identifier: GitHub repository identifier (owner/repo)
        target_dir: Target directory for .git folder (optional)
        github_token: GitHub API token (optional, uses env var if not provided)

    Returns:
        JSON string with download result and .git folder path
    """
    logger.info(f"Starting .git folder download for GitHub repo: {repo_identifier}")

    try:
        # Validate repository identifier
        if not repo_identifier or repo_identifier.count('/') != 1:
            raise ValueError("Repository identifier must be in format 'owner/repo'")

        # Create target directory
        if target_dir is None:
            repo_name = repo_identifier.split('/')[1]
            target_dir = tempfile.mkdtemp(prefix=f"git_folder_{repo_name}_")

        git_dir = Path(target_dir) / ".git"

        # Download using clone + extract method
        logger.info("Downloading .git folder using shallow clone...")
        result = _download_github_clone_method(repo_identifier, git_dir, github_token)

        logger.info(f"Successfully downloaded .git folder to: {git_dir}")

        return json.dumps({
            "success": True,
            "method": "shallow_clone",
            "repo_identifier": repo_identifier,
            "git_folder_path": str(git_dir),
            "target_directory": target_dir,
            "download_stats": result,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

    except Exception as e:
        logger.error(f"Failed to download .git folder: {str(e)}")
        return json.dumps({
            "success": False,
            "error": str(e),
            "repo_identifier": repo_identifier,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }, indent=2)

def _download_github_clone_method(repo_identifier: str, git_dir: Path, github_token: Optional[str]) -> Dict[str, Any]:
    """
    Reliable method: Clone repository and extract .git folder.
    
    This method performs a shallow clone and then extracts only the .git folder,
    removing the working directory. This is the fallback method that should always work.
    """
    temp_clone_dir = None
    try:
        # Create temporary directory for cloning
        temp_clone_dir = tempfile.mkdtemp(prefix="git_clone_temp_")
        
        # Construct clone URL with token if provided
        token = github_token or os.getenv('GITHUB_TOKEN')
        if token:
            clone_url = f"https://{token}@github.com/{repo_identifier}.git"
        else:
            clone_url = f"https://github.com/{repo_identifier}.git"
        
        # Perform shallow clone
        logger.info(f"Cloning repository {repo_identifier}...")
        result = subprocess.run([
            'git', 'clone', '--depth', '1', '--no-checkout', clone_url, temp_clone_dir
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode != 0:
            raise Exception(f"Git clone failed: {result.stderr}")
        
        # Copy .git folder to target location
        source_git_dir = Path(temp_clone_dir) / ".git"
        if not source_git_dir.exists():
            raise Exception("No .git folder found in cloned repository")
        
        # Ensure target directory exists
        git_dir.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy .git folder
        if git_dir.exists():
            shutil.rmtree(git_dir)
        shutil.copytree(source_git_dir, git_dir)
        
        # Get statistics
        stats = _get_git_folder_stats(git_dir)
        
        logger.info(f"Successfully extracted .git folder to: {git_dir}")
        
        return stats
        
    except subprocess.TimeoutExpired:
        raise Exception("Git clone operation timed out")
    except Exception as e:
        raise Exception(f"Clone method failed: {str(e)}")
    finally:
        # Clean up temporary clone directory
        if temp_clone_dir and Path(temp_clone_dir).exists():
            try:
                shutil.rmtree(temp_clone_dir)
                logger.debug(f"Cleaned up temporary clone directory: {temp_clone_dir}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary directory: {e}")

def _get_git_folder_stats(git_dir: Path) -> Dict[str, Any]:
    """Get statistics about the downloaded .git folder."""
    stats = {
        "method": "shallow_clone",
        "git_folder_size_bytes": 0,
        "refs_found": 0,
        "objects_found": 0,
        "branches_found": 0,
        "tags_found": 0
    }
    
    try:
        # Calculate folder size
        total_size = 0
        for dirpath, _, filenames in os.walk(git_dir):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
        stats["git_folder_size_bytes"] = total_size
        
        # Count refs
        refs_dir = git_dir / "refs"
        if refs_dir.exists():
            heads_dir = refs_dir / "heads"
            if heads_dir.exists():
                stats["branches_found"] = len(list(heads_dir.glob("*")))
            
            tags_dir = refs_dir / "tags"
            if tags_dir.exists():
                stats["tags_found"] = len(list(tags_dir.glob("*")))
            
            stats["refs_found"] = stats["branches_found"] + stats["tags_found"]
        
        # Count objects
        objects_dir = git_dir / "objects"
        if objects_dir.exists():
            object_count = 0
            for obj_dir in objects_dir.iterdir():
                if obj_dir.is_dir() and len(obj_dir.name) == 2:
                    object_count += len(list(obj_dir.glob("*")))
            stats["objects_found"] = object_count
        
    except Exception as e:
        logger.debug(f"Error calculating stats: {e}")
    
    return stats

# Create the Git Folder Downloader Agent
root_agent = LlmAgent(
    name="GitFolderDownloader",
    model="gemini-2.0-flash",
    instruction="""You are a specialized GitHub .git Folder Downloader AI that efficiently downloads only the .git folder from GitHub repositories.

## Core Capabilities:

### Efficient .git Folder Download:
- Download only .git folder contents (no working directory)
- Use shallow clone method for maximum reliability
- Authenticate with GitHub tokens for private repositories
- No cleanup required (automatic temporary file management)
- Much faster than full repository cloning

### GitHub-Only Focus:
- **GitHub Repositories**: Full support for github.com repositories
- **Public & Private**: Works with both public and private repositories
- **Enterprise GitHub**: Compatible with GitHub Enterprise instances
- **Token Authentication**: Optional GitHub token for enhanced access

### Security Features:
- Token-based authentication for private repositories
- Input validation and error handling
- Secure temporary file management
- Production logging and monitoring

## Available Tool:
- `download_git_folder_from_github`: Download .git folder from GitHub repository

## Usage Examples:
- "Download .git folder from microsoft/vscode"
- "Get .git data from facebook/react repository"
- "Download private repository owner/private-repo .git folder"

## Benefits Over Traditional Cloning:
- **Faster**: Only downloads Git metadata, not file contents
- **Efficient**: Minimal bandwidth and storage usage
- **Reliable**: Uses proven Git clone method with shallow depth
- **Clean**: Automatic temporary file cleanup
- **Scalable**: Can handle repositories of any size efficiently

Focus on providing the .git folder path for subsequent analysis by other agents.
Always use GitHub tokens for private repositories and provide clear error messages.
""",
    description="Downloads only .git folders from GitHub repositories for efficient Git analysis.",
    tools=[download_git_folder_from_github],
    output_key="git_folder_path"
)
