"""
Configuration for Enhanced GitHub Agent

This module contains all configuration settings for the enhanced GitHub agent
including cache timeouts, parallel processing settings, and fallback mechanisms.
"""

import os
from typing import Dict, Any

# Cache Configuration
CACHE_TIMEOUT_MINUTES = int(os.getenv('GITHUB_CACHE_TIMEOUT_MINUTES', '30'))
CACHE_STORAGE_PATH = os.getenv('GITHUB_CACHE_PATH', '/tmp/github_cache')
PERSISTENT_STORAGE_ENABLED = os.getenv('GITHUB_PERSISTENT_STORAGE', 'true').lower() == 'true'

# Google Cloud Storage Configuration (for production)
GCS_BUCKET_NAME = os.getenv('GITHUB_CACHE_GCS_BUCKET', 'enterprise-github-cache')
GCS_PROJECT_ID = os.getenv('GOOGLE_CLOUD_PROJECT')
GCS_ENABLED = os.getenv('GITHUB_USE_GCS', 'false').lower() == 'true'

# Parallel Processing Configuration
PARALLEL_DOWNLOAD_ENABLED = os.getenv('GITHUB_PARALLEL_DOWNLOAD', 'true').lower() == 'true'
DOWNLOAD_TIMEOUT_SECONDS = int(os.getenv('GITHUB_DOWNLOAD_TIMEOUT', '300'))
MAX_CONCURRENT_DOWNLOADS = int(os.getenv('GITHUB_MAX_CONCURRENT_DOWNLOADS', '3'))

# Fallback Configuration
ENABLE_MCP_FALLBACK = os.getenv('GITHUB_ENABLE_MCP_FALLBACK', 'true').lower() == 'true'
MCP_SERVER_URL = os.getenv('GITHUB_MCP_SERVER_URL', 'http://localhost:8080')
MCP_TIMEOUT_SECONDS = int(os.getenv('GITHUB_MCP_TIMEOUT', '30'))

# Analysis Configuration
DETAILED_ANALYSIS_ENABLED = os.getenv('GITHUB_DETAILED_ANALYSIS', 'true').lower() == 'true'
MAX_COMMITS_ANALYZE = int(os.getenv('GITHUB_MAX_COMMITS_ANALYZE', '1000'))
ENABLE_DEVELOPER_METRICS = os.getenv('GITHUB_DEVELOPER_METRICS', 'true').lower() == 'true'

# Retry Configuration
MAX_DOWNLOAD_RETRIES = int(os.getenv('GITHUB_MAX_DOWNLOAD_RETRIES', '3'))
RETRY_BACKOFF_SECONDS = int(os.getenv('GITHUB_RETRY_BACKOFF', '5'))

# Logging Configuration
LOG_LEVEL = os.getenv('GITHUB_LOG_LEVEL', 'INFO')
ENABLE_PERFORMANCE_LOGGING = os.getenv('GITHUB_PERFORMANCE_LOGGING', 'true').lower() == 'true'
ENABLE_AUDIT_LOGGING = os.getenv('GITHUB_AUDIT_LOGGING', 'true').lower() == 'true'

# Security Configuration
GITHUB_TOKEN_REQUIRED = os.getenv('GITHUB_TOKEN_REQUIRED', 'false').lower() == 'true'
VALIDATE_REPO_ACCESS = os.getenv('GITHUB_VALIDATE_REPO_ACCESS', 'true').lower() == 'true'

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary."""
    return {
        'cache': {
            'timeout_minutes': CACHE_TIMEOUT_MINUTES,
            'storage_path': CACHE_STORAGE_PATH,
            'persistent_enabled': PERSISTENT_STORAGE_ENABLED,
            'gcs_bucket': GCS_BUCKET_NAME,
            'gcs_project': GCS_PROJECT_ID,
            'gcs_enabled': GCS_ENABLED
        },
        'parallel_processing': {
            'enabled': PARALLEL_DOWNLOAD_ENABLED,
            'download_timeout': DOWNLOAD_TIMEOUT_SECONDS,
            'max_concurrent': MAX_CONCURRENT_DOWNLOADS
        },
        'fallback': {
            'mcp_enabled': ENABLE_MCP_FALLBACK,
            'mcp_server_url': MCP_SERVER_URL,
            'mcp_timeout': MCP_TIMEOUT_SECONDS
        },
        'analysis': {
            'detailed_enabled': DETAILED_ANALYSIS_ENABLED,
            'max_commits': MAX_COMMITS_ANALYZE,
            'developer_metrics': ENABLE_DEVELOPER_METRICS
        },
        'retry': {
            'max_retries': MAX_DOWNLOAD_RETRIES,
            'backoff_seconds': RETRY_BACKOFF_SECONDS
        },
        'logging': {
            'level': LOG_LEVEL,
            'performance': ENABLE_PERFORMANCE_LOGGING,
            'audit': ENABLE_AUDIT_LOGGING
        },
        'security': {
            'token_required': GITHUB_TOKEN_REQUIRED,
            'validate_access': VALIDATE_REPO_ACCESS
        }
    }

def get_cache_timeout_seconds() -> int:
    """Get cache timeout in seconds."""
    return CACHE_TIMEOUT_MINUTES * 60

def is_cache_expired(last_modified_timestamp: float) -> bool:
    """Check if cache is expired based on timestamp."""
    import time
    current_time = time.time()
    age_seconds = current_time - last_modified_timestamp
    return age_seconds > get_cache_timeout_seconds()

def get_storage_path(repo_identifier: str) -> str:
    """Get storage path for a repository."""
    import os
    from pathlib import Path
    
    safe_repo_name = repo_identifier.replace('/', '_').replace('\\', '_')
    storage_path = Path(CACHE_STORAGE_PATH) / safe_repo_name
    
    # Ensure directory exists
    storage_path.mkdir(parents=True, exist_ok=True)
    
    return str(storage_path)
